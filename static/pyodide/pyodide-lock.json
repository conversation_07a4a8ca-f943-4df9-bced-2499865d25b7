{"info": {"abi_version": "2024_0", "arch": "wasm32", "platform": "emscripten_3_1_58", "python": "3.12.7", "version": "0.27.7"}, "packages": {"affine": {"depends": [], "file_name": "affine-2.4.0-py3-none-any.whl", "imports": ["affine"], "install_dir": "site", "name": "affine", "package_type": "package", "sha256": "c9323a8bd2d1d98e41815810f66ecb749943728a192f25c2b80d0088bda8d823", "unvendored_tests": true, "version": "2.4.0"}, "affine-tests": {"depends": ["affine"], "file_name": "affine-tests.tar", "imports": [], "install_dir": "site", "name": "affine-tests", "package_type": "package", "sha256": "761c61558dfa1d429b4f6affaf4065ad1094997a7ebe1a68cf5a1cc0cfee90dc", "unvendored_tests": false, "version": "2.4.0"}, "aiohttp": {"depends": ["aiosignal", "async-timeout", "attrs", "charset-normalizer", "frozenlist", "multidict", "yarl"], "file_name": "aiohttp-3.9.5-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["aiohttp"], "install_dir": "site", "name": "aiohttp", "package_type": "package", "sha256": "b4bdd9c7329e0594fe6649b8b33daaf6773804eb5d79f8a8af3719e1c284b5d2", "unvendored_tests": true, "version": "3.9.5"}, "aiohttp-tests": {"depends": ["aiohttp"], "file_name": "aiohttp-tests.tar", "imports": [], "install_dir": "site", "name": "aiohttp-tests", "package_type": "package", "sha256": "deda3e43e0fb8e2125fe856e10d65de3403aada4cdf53530813cfe7f3b740c3e", "unvendored_tests": false, "version": "3.9.5"}, "aiosignal": {"depends": ["frozenlist"], "file_name": "aiosignal-1.3.1-py3-none-any.whl", "imports": ["aiosignal"], "install_dir": "site", "name": "aiosignal", "package_type": "package", "sha256": "1314574b5d7ab2c91dc5cfcd85e24fd0adf40e4dbf5845f347d17aa21f1aacfc", "unvendored_tests": false, "version": "1.3.1"}, "altair": {"depends": ["typing-extensions", "jinja2", "jsonschema", "packaging", "narwhals"], "file_name": "altair-5.4.1-py3-none-any.whl", "imports": ["altair"], "install_dir": "site", "name": "altair", "package_type": "package", "sha256": "d08aff5d4e101b7a0f9ae1d8ba1fe1d1f6bac0b1e9b1b90b7ccbd9ba3993f0f5", "unvendored_tests": false, "version": "5.4.1"}, "annotated-types": {"depends": [], "file_name": "annotated_types-0.6.0-py3-none-any.whl", "imports": ["annotated_types"], "install_dir": "site", "name": "annotated-types", "package_type": "package", "sha256": "9733de5d59580defb40ad7bd8a5d53a0fac03de7a13028d95cacc4e81945c97c", "unvendored_tests": true, "version": "0.6.0"}, "annotated-types-tests": {"depends": ["annotated-types"], "file_name": "annotated-types-tests.tar", "imports": [], "install_dir": "site", "name": "annotated-types-tests", "package_type": "package", "sha256": "691581bfe36b9e7351e1efa80f64ad970d0931ff468b05dd0bc1e229d6702916", "unvendored_tests": false, "version": "0.6.0"}, "anyio": {"depends": ["ssl", "sniffio", "typing-extensions"], "file_name": "anyio-4.9.0-py3-none-any.whl", "imports": ["anyio"], "install_dir": "site", "name": "anyio", "package_type": "package", "sha256": "e1d9180d4361fd71d1bc4a7007fea6cae1d18792dba9d07eaad89f2a8562f71c", "unvendored_tests": false, "version": "4.9.0"}, "apsw": {"depends": [], "file_name": "apsw-3.47.2.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["apsw"], "install_dir": "site", "name": "apsw", "package_type": "package", "sha256": "dedec1a73c98566d03b9e0985a531219387a780dbac9811fd56ae73394a7ef44", "unvendored_tests": false, "version": "3.47.2.0"}, "argon2-cffi": {"depends": ["argon2-cffi-bindings"], "file_name": "argon2_cffi-23.1.0-py3-none-any.whl", "imports": ["argon2"], "install_dir": "site", "name": "argon2-cffi", "package_type": "package", "sha256": "630137502ab48440d9bf8f5a206ba439e84728f686fb0326bcc08ab08750350d", "unvendored_tests": false, "version": "23.1.0"}, "argon2-cffi-bindings": {"depends": ["cffi"], "file_name": "argon2_cffi_bindings-21.2.0-cp312-abi3-pyodide_2024_0_wasm32.whl", "imports": ["_argon2_cffi_bindings"], "install_dir": "site", "name": "argon2-cffi-bindings", "package_type": "package", "sha256": "1657e6db06945e26809888c581ef511f27e3d6eb726d6108355133786cf4f5d9", "unvendored_tests": false, "version": "21.2.0"}, "arro3-compute": {"depends": ["arro3-core"], "file_name": "arro3_compute-0.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["arro3.compute"], "install_dir": "site", "name": "arro3-compute", "package_type": "package", "sha256": "6fbd4073d8728ee8a540a80e4311cce4a91057b8c85f2c05e3b6fddbf3cf5758", "unvendored_tests": false, "version": "0.4.1"}, "arro3-core": {"depends": [], "file_name": "arro3_core-0.4.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["arro3.core"], "install_dir": "site", "name": "arro3-core", "package_type": "package", "sha256": "249ecb1f0d38cdcff25f059625e676405579015363da8fc9cb6d53f4def999db", "unvendored_tests": false, "version": "0.4.1"}, "arro3-io": {"depends": ["arro3-core"], "file_name": "arro3_io-0.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["arro3.io"], "install_dir": "site", "name": "arro3-io", "package_type": "package", "sha256": "8bb9a2a9b9b16668097e0964d04292a2f37c0d4d43fad506a35427a10855d9da", "unvendored_tests": false, "version": "0.4.1"}, "asciitree": {"depends": [], "file_name": "asciitree-0.3.3-py3-none-any.whl", "imports": ["as<PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "as<PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "3ac0c8cd8b0dba1fd619696a55b30712839d114cb00ee6723bcf683758e54129", "unvendored_tests": false, "version": "0.3.3"}, "astropy": {"depends": ["packaging", "numpy", "pyerfa", "pyyaml", "astropy_iers_data"], "file_name": "astropy-7.0.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["astropy"], "install_dir": "site", "name": "astropy", "package_type": "package", "sha256": "b2c68b140367816c8b49f11f89baa461da557d5ee8d084b46c0d611cc41645ca", "unvendored_tests": false, "version": "7.0.0"}, "astropy-iers-data": {"depends": [], "file_name": "astropy_iers_data-0.2024.4.22.0.29.50-py3-none-any.whl", "imports": ["astropy_iers_data"], "install_dir": "site", "name": "astropy_iers_data", "package_type": "package", "sha256": "b6e215225866edbb309b05882ca29535b50ef5cb81a806481b11767e51ccb2b2", "unvendored_tests": true, "version": "0.2024.4.22.0.29.50"}, "astropy-iers-data-tests": {"depends": ["astropy_iers_data"], "file_name": "astropy-iers-data-tests.tar", "imports": [], "install_dir": "site", "name": "astropy_iers_data-tests", "package_type": "package", "sha256": "e3345f022de75aa46d54f3b30bcca6cf6d1e07e8b921c2d6f90634c7842f466d", "unvendored_tests": false, "version": "0.2024.4.22.0.29.50"}, "asttokens": {"depends": ["six"], "file_name": "asttokens-2.4.1-py2.py3-none-any.whl", "imports": ["asttokens"], "install_dir": "site", "name": "asttokens", "package_type": "package", "sha256": "bfeb9073458201c1f1c03a830e2154df70429d156d47844db30a19e2a7351e55", "unvendored_tests": false, "version": "2.4.1"}, "async-timeout": {"depends": [], "file_name": "async_timeout-4.0.3-py3-none-any.whl", "imports": ["async_timeout"], "install_dir": "site", "name": "async-timeout", "package_type": "package", "sha256": "7fa8d0e5425e7347978ff7d8ca5446748839177de98c0ae9930af03faf1fcc7a", "unvendored_tests": false, "version": "4.0.3"}, "atomicwrites": {"depends": [], "file_name": "atomicwrites-1.4.1-py2.py3-none-any.whl", "imports": ["atomicwrites"], "install_dir": "site", "name": "atomicwrites", "package_type": "package", "sha256": "f2518238e4f900f3c8adc48fc5e6dfe74db6bf650bb63698fa0a0d48e2567b9d", "unvendored_tests": false, "version": "1.4.1"}, "attrs": {"depends": ["six"], "file_name": "attrs-23.2.0-py3-none-any.whl", "imports": ["attr", "attrs"], "install_dir": "site", "name": "attrs", "package_type": "package", "sha256": "0ee53cdb902382d5ae8f13992baaa506073dcaef8bda1be86cfba145ee40cd22", "unvendored_tests": false, "version": "23.2.0"}, "autograd": {"depends": ["numpy", "future"], "file_name": "autograd-1.7.0-py3-none-any.whl", "imports": ["autograd"], "install_dir": "site", "name": "autograd", "package_type": "package", "sha256": "262af11549c493d3ac084030269e14696a9148b263539c681e5c60d35e223ec2", "unvendored_tests": true, "version": "1.7.0"}, "autograd-tests": {"depends": ["autograd"], "file_name": "autograd-tests.tar", "imports": [], "install_dir": "site", "name": "autograd-tests", "package_type": "package", "sha256": "cc6a9470407adba2109657547f39c9b4494c9769d5114545baed51ae499cc687", "unvendored_tests": false, "version": "1.7.0"}, "awkward-cpp": {"depends": ["numpy"], "file_name": "awkward_cpp-44-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["awkward_cpp"], "install_dir": "site", "name": "awkward-cpp", "package_type": "package", "sha256": "9092e80a0c9f6e933700423d45a0e2df8962cf6c638930d8b5099452f3ec0809", "unvendored_tests": false, "version": "44"}, "b2d": {"depends": ["numpy", "pydantic", "setuptools", "annotated-types"], "file_name": "b2d-0.7.4-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["b2d"], "install_dir": "site", "name": "b2d", "package_type": "package", "sha256": "0476a6ab1a6d9ae963d3a97aa317d660b79a571297fdde0158a2e504c3435786", "unvendored_tests": false, "version": "0.7.4"}, "bcrypt": {"depends": [], "file_name": "bcrypt-4.1.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["bcrypt"], "install_dir": "site", "name": "bcrypt", "package_type": "package", "sha256": "1836a80e3a030dec7ba3dd82a59c2f3162c83d0ea473972dce7f1a4702afdcd3", "unvendored_tests": false, "version": "4.1.2"}, "beautifulsoup4": {"depends": ["soupsieve"], "file_name": "<PERSON><PERSON>up4-4.12.3-py3-none-any.whl", "imports": ["bs4"], "install_dir": "site", "name": "beautifulsoup4", "package_type": "package", "sha256": "dadfaf468a0acbd3068e5ad8cfce9f7c364a58161307ccc49ab2452b4956e13b", "unvendored_tests": true, "version": "4.12.3"}, "beautifulsoup4-tests": {"depends": ["beautifulsoup4"], "file_name": "beautifulsoup4-tests.tar", "imports": [], "install_dir": "site", "name": "beautifulsoup4-tests", "package_type": "package", "sha256": "d7a5f6a3236dcca4144c69a6eed6e36363adc6c6b9b4eac59dc92f869181860e", "unvendored_tests": false, "version": "4.12.3"}, "biopython": {"depends": ["numpy"], "file_name": "biopython-1.84-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["Bio", "BioSQL"], "install_dir": "site", "name": "biopython", "package_type": "package", "sha256": "35e88ba1455c9fdc02f78cdd7193137031c837d5efded493319e4acfb32cffb6", "unvendored_tests": false, "version": "1.84"}, "bitarray": {"depends": [], "file_name": "bitarray-2.9.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["bitarray"], "install_dir": "site", "name": "bitarray", "package_type": "package", "sha256": "56794ec14351c590ed4d9cedc9bef3cd0a3d28fbdfaed161d17c208bf7bbb766", "unvendored_tests": true, "version": "2.9.2"}, "bitarray-tests": {"depends": ["bitarray"], "file_name": "bitarray-tests.tar", "imports": [], "install_dir": "site", "name": "bitarray-tests", "package_type": "package", "sha256": "bc8a637cb3b2a839be949245ce8ee699d8fded2b5c50baf3a92ce52df776d3cf", "unvendored_tests": false, "version": "2.9.2"}, "bitstring": {"depends": ["bitarray"], "file_name": "bitstring-4.1.4-py3-none-any.whl", "imports": ["bitstring"], "install_dir": "site", "name": "bitstring", "package_type": "package", "sha256": "30a87a2037106772804bc253342df5df13b982145377ec4cd096899ca4303f4c", "unvendored_tests": false, "version": "4.1.4"}, "bleach": {"depends": ["webencodings", "packaging", "six"], "file_name": "bleach-6.1.0-py3-none-any.whl", "imports": ["bleach"], "install_dir": "site", "name": "bleach", "package_type": "package", "sha256": "961d704d008acabfe76acd1d6a1d74698cc8a855b838e507aa464e0b6ec40a42", "unvendored_tests": false, "version": "6.1.0"}, "bokeh": {"depends": ["contourpy", "numpy", "jinja2", "pandas", "pillow", "python-dateutil", "six", "typing-extensions", "pyyaml", "xyzservices"], "file_name": "bokeh-3.6.0-py3-none-any.whl", "imports": ["bokeh"], "install_dir": "site", "name": "bokeh", "package_type": "package", "sha256": "897ad588d4724f83ce0476838fe0f2d32aba7fe28daa0f8347eab06569631a90", "unvendored_tests": false, "version": "3.6.0"}, "boost-histogram": {"depends": ["numpy"], "file_name": "boost_histogram-1.5.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["boost_histogram"], "install_dir": "site", "name": "boost-histogram", "package_type": "package", "sha256": "a685186d8f3656bf031d1ebb3e6cc3903d6158fbb4981705c1feb8d02124fa90", "unvendored_tests": false, "version": "1.5.0"}, "brotli": {"depends": [], "file_name": "brotli-1.1.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["brotli"], "install_dir": "site", "name": "brotli", "package_type": "package", "sha256": "182ec62565262bcb26448596586f171c77d2dbaa4d440907dd8b99590222367c", "unvendored_tests": false, "version": "1.1.0"}, "buffer-test": {"depends": [], "file_name": "buffer_test-0.1.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["buffer_test"], "install_dir": "site", "name": "buffer-test", "package_type": "package", "sha256": "10c9ebed5601166dc8fb20daabd58fba388a53f37812ffb0bf7d98dceb17d32e", "unvendored_tests": false, "version": "0.1.1"}, "cachetools": {"depends": [], "file_name": "cachetools-5.3.3-py3-none-any.whl", "imports": ["cachetools"], "install_dir": "site", "name": "cachetools", "package_type": "package", "sha256": "eff8dd237ade14c81f23b404dd2fb0160304b9f96943bdd4e4ec0e24de946542", "unvendored_tests": false, "version": "5.3.3"}, "cartopy": {"depends": ["shapely", "pyshp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "geos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scipy"], "file_name": "cartopy-0.24.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cartopy"], "install_dir": "site", "name": "Cartopy", "package_type": "package", "sha256": "9f27c8deb3e9f232272a8595b79b61a66a5cec82886c89ac0413f5a241b1eda6", "unvendored_tests": true, "version": "0.24.1"}, "cartopy-tests": {"depends": ["Cartopy"], "file_name": "cartopy-tests.tar", "imports": [], "install_dir": "site", "name": "Cartopy-tests", "package_type": "package", "sha256": "d291e7ee14a0a3f5e935791c87e9e4a1f93e843e850cd00f144a85eb48de2caa", "unvendored_tests": false, "version": "0.24.1"}, "casadi": {"depends": ["numpy"], "file_name": "casadi-3.6.7-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["casadi"], "install_dir": "site", "name": "casadi", "package_type": "package", "sha256": "c347ca18e190c7ed0f69f28b26d7f12627e865bc219da5041c749f3e647b9d67", "unvendored_tests": false, "version": "3.6.7"}, "cbor-diag": {"depends": [], "file_name": "cbor_diag-1.0.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["cbor_diag"], "install_dir": "site", "name": "cbor-diag", "package_type": "package", "sha256": "06f9c6a26c12def09ff8ebf5b31c00ab53091e8ad37770339f87970dfafca949", "unvendored_tests": false, "version": "1.0.1"}, "certifi": {"depends": [], "file_name": "certifi-2024.12.14-py3-none-any.whl", "imports": ["certifi"], "install_dir": "site", "name": "certifi", "package_type": "package", "sha256": "a5f31d8fbcd66632ba9fe6face6471c4d4df2e85cd6c46bd0c5ff58eb903a4ad", "unvendored_tests": false, "version": "2024.12.14"}, "cffi": {"depends": ["pyc<PERSON><PERSON>"], "file_name": "cffi-1.17.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cffi"], "install_dir": "site", "name": "cffi", "package_type": "package", "sha256": "3844b091b8da880622d16c428a545d6a2753dfc148240f1328019375e6f70868", "unvendored_tests": false, "version": "1.17.1"}, "cffi-example": {"depends": ["cffi"], "file_name": "cffi_example-0.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["cffi_example"], "install_dir": "site", "name": "cffi_example", "package_type": "package", "sha256": "a70ec7a3ad385297c242d6c6f1c05fb0bc2ff3148586514323b5fb128591ef3d", "unvendored_tests": false, "version": "0.1"}, "cftime": {"depends": ["numpy"], "file_name": "cftime-1.6.4.post1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cftime"], "install_dir": "site", "name": "cftime", "package_type": "package", "sha256": "485abc24c9539fa7d4db3ebbac812e2472921dfd1ef1ed6290008856dc2164a1", "unvendored_tests": false, "version": "1.6.4.post1"}, "charset-normalizer": {"depends": [], "file_name": "charset_normalizer-3.3.2-py3-none-any.whl", "imports": ["charset_normalizer"], "install_dir": "site", "name": "charset-normalizer", "package_type": "package", "sha256": "b83440eee4a1398f9a71186169cdc17b84cdb93a63e61f3c6347dd1463a11fca", "unvendored_tests": false, "version": "3.3.2"}, "clarabel": {"depends": ["numpy", "scipy"], "file_name": "clarabel-0.9.0-cp37-abi3-pyodide_2024_0_wasm32.whl", "imports": ["clarabel"], "install_dir": "site", "name": "clarabel", "package_type": "package", "sha256": "ea23a4776d99d9b20e1ddf2494f87fac9ecee00557c9228cdb1571c1e1f787e4", "unvendored_tests": false, "version": "0.9.0"}, "click": {"depends": [], "file_name": "click-8.1.7-py3-none-any.whl", "imports": ["click"], "install_dir": "site", "name": "click", "package_type": "package", "sha256": "b775249c5e122a827749407ad5540e6ac9a4b5a07692080993b8d95f5f82cb3a", "unvendored_tests": false, "version": "8.1.7"}, "cligj": {"depends": ["click"], "file_name": "cligj-0.7.2-py3-none-any.whl", "imports": ["cligj"], "install_dir": "site", "name": "cligj", "package_type": "package", "sha256": "a8b27eb22645f5199f9b706a513933d7c3803da47dceeea047bf3a2d12bcd761", "unvendored_tests": false, "version": "0.7.2"}, "clingo": {"depends": ["cffi"], "file_name": "clingo-5.7.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["clingo"], "install_dir": "site", "name": "clingo", "package_type": "package", "sha256": "4a8b320d1586393d589aae2dbca3c4b2c7711b48578910562e03389f7ed71732", "unvendored_tests": false, "version": "5.7.1"}, "cloudpickle": {"depends": [], "file_name": "cloudpickle-3.0.0-py3-none-any.whl", "imports": ["cloudpickle"], "install_dir": "site", "name": "cloudpickle", "package_type": "package", "sha256": "9625085cc9e9f1a3fbd2b22f7cab1277afdda4b091136d68a98a115659f6d993", "unvendored_tests": false, "version": "3.0.0"}, "cmyt": {"depends": ["colorspacious", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "more-itertools", "numpy"], "file_name": "cmyt-2.0.0-py3-none-any.whl", "imports": ["cmyt"], "install_dir": "site", "name": "cmyt", "package_type": "package", "sha256": "8a84417a5bb77a22b18c497af95397194f9322e2f61f1846e05f3526057095e4", "unvendored_tests": true, "version": "2.0.0"}, "cmyt-tests": {"depends": ["cmyt"], "file_name": "cmyt-tests.tar", "imports": [], "install_dir": "site", "name": "cmyt-tests", "package_type": "package", "sha256": "9918a274a353ec6228a3fe85bcc8c1526fdd9b31a3abac76a56b8a15d06ef6d0", "unvendored_tests": false, "version": "2.0.0"}, "colorspacious": {"depends": ["numpy"], "file_name": "colorspacious-1.1.2-py2.py3-none-any.whl", "imports": ["colorspacious"], "install_dir": "site", "name": "colorspacious", "package_type": "package", "sha256": "d0c49422117b29773267f61eede2f1010753889af797ddc9c14f0f227e7241c7", "unvendored_tests": false, "version": "1.1.2"}, "contourpy": {"depends": ["numpy"], "file_name": "contourpy-1.3.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["contourpy"], "install_dir": "site", "name": "contourpy", "package_type": "package", "sha256": "f6ca0437a039c40691be53ff5bd3c9774868add8d5c2d032167495c6ff1f1c4b", "unvendored_tests": false, "version": "1.3.0"}, "coolprop": {"depends": ["numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "coolprop-6.6.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["CoolProp"], "install_dir": "site", "name": "coolprop", "package_type": "package", "sha256": "e1a3b369385520e9c363f406a572c202fdb279c7378b598f4b24be2ff06d80ee", "unvendored_tests": true, "version": "6.6.0"}, "coolprop-tests": {"depends": ["coolprop"], "file_name": "coolprop-tests.tar", "imports": [], "install_dir": "site", "name": "coolprop-tests", "package_type": "package", "sha256": "24c8b6e386b46c6bf95ef70c6a378cc7bf8333bf10ece309719f39d9b0a4e36a", "unvendored_tests": false, "version": "6.6.0"}, "coverage": {"depends": ["sqlite3"], "file_name": "coverage-7.4.4-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["coverage"], "install_dir": "site", "name": "coverage", "package_type": "package", "sha256": "d26ca50b543df98d77de221462b691ab33f49b997f07abf43957e2f078e06a18", "unvendored_tests": false, "version": "7.4.4"}, "cpp-exceptions-test": {"depends": [], "file_name": "cpp-exceptions-test-0.1.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "cpp-exceptions-test", "package_type": "shared_library", "sha256": "a735e762e9aa55e6008086f70cd7c2e9dda8eb01e271277d4b2c8d1ac905133e", "unvendored_tests": false, "version": "0.1"}, "cpp-exceptions-test2": {"depends": [], "file_name": "cpp_exceptions_test2-1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["cpp-exceptions-test2"], "install_dir": "site", "name": "cpp-exceptions-test2", "package_type": "package", "sha256": "6290e7e33f2be1f6a4f931c1cc526807ac5b5d291d482e0fc4603b31785e3034", "unvendored_tests": false, "version": "1.0"}, "cramjam": {"depends": [], "file_name": "cramjam-2.8.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cramjam"], "install_dir": "site", "name": "cramjam", "package_type": "package", "sha256": "da6bef32a15029038cd90aa561503462000a046c39fd6061e6ffa8c74e0dca13", "unvendored_tests": false, "version": "2.8.3"}, "crc32c": {"depends": [], "file_name": "crc32c-2.7.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["crc32c"], "install_dir": "site", "name": "crc32c", "package_type": "package", "sha256": "dddd5d0d25a60c0709c82c2351f8693b016ec6b0189a0cb9122e15cf88ca728c", "unvendored_tests": false, "version": "2.7.1"}, "cryptography": {"depends": ["openssl", "six", "cffi"], "file_name": "cryptography-42.0.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cryptography"], "install_dir": "site", "name": "cryptography", "package_type": "package", "sha256": "c336f40273d92660cfa8bc2b4180c197ee733d4582e9201be5397072715c6436", "unvendored_tests": false, "version": "42.0.5"}, "css-inline": {"depends": [], "file_name": "css_inline-0.14.6-cp37-abi3-pyodide_2024_0_wasm32.whl", "imports": ["css_inline"], "install_dir": "site", "name": "css-inline", "package_type": "package", "sha256": "863c3e7a93577ef93c76ed6653b68be31f5220dead771e190dce076679fa8daf", "unvendored_tests": false, "version": "0.14.6"}, "cssselect": {"depends": [], "file_name": "cssselect-1.2.0-py2.py3-none-any.whl", "imports": ["cssselect"], "install_dir": "site", "name": "cssselect", "package_type": "package", "sha256": "4514b2ffe110a100b0430737debd989421a75472c47e5e09f09b36f81e81a6e2", "unvendored_tests": false, "version": "1.2.0"}, "cvxpy-base": {"depends": ["numpy", "scipy", "clarabel"], "file_name": "cvxpy_base-1.5.1-py3-none-any.whl", "imports": ["cvxpy"], "install_dir": "site", "name": "cvxpy-base", "package_type": "package", "sha256": "74147367dfe5e688420bc612335681aef26ac0e3770db5c56291b761ed4bcb99", "unvendored_tests": true, "version": "1.5.1"}, "cvxpy-base-tests": {"depends": ["cvxpy-base"], "file_name": "cvxpy-base-tests.tar", "imports": [], "install_dir": "site", "name": "cvxpy-base-tests", "package_type": "package", "sha256": "aeab1ec32f46deca69ed23a4393683a07d13da7f7b97b2a6be2745f5570cdcf9", "unvendored_tests": false, "version": "1.5.1"}, "cycler": {"depends": ["six"], "file_name": "cycler-0.12.1-py3-none-any.whl", "imports": ["cycler"], "install_dir": "site", "name": "cycler", "package_type": "package", "sha256": "e33687b4269fe3eda13c2d38006f9a5daa602c95f5c4f8d0576aff0717d001a6", "unvendored_tests": false, "version": "0.12.1"}, "cysignals": {"depends": [], "file_name": "cysignals-1.12.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cysignals"], "install_dir": "site", "name": "cysignals", "package_type": "package", "sha256": "3506506c50cf26fd1fb09874f6561e9faca98eab37fd0b114258226bc3131067", "unvendored_tests": false, "version": "1.12.2"}, "cytoolz": {"depends": ["toolz"], "file_name": "cytoolz-0.12.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["cytoolz"], "install_dir": "site", "name": "cytoolz", "package_type": "package", "sha256": "dc4027bdec5336ece9480c651a72ee35749b0f76c8e5801f67be34125766e0c8", "unvendored_tests": true, "version": "0.12.3"}, "cytoolz-tests": {"depends": ["cytoolz"], "file_name": "cytoolz-tests.tar", "imports": [], "install_dir": "site", "name": "cytoolz-tests", "package_type": "package", "sha256": "820bb9e3afa033a112cf82f57cf11fa5f2877b86464795ca8af2742280e8ea43", "unvendored_tests": false, "version": "0.12.3"}, "decorator": {"depends": [], "file_name": "decorator-5.1.1-py3-none-any.whl", "imports": ["decorator"], "install_dir": "site", "name": "decorator", "package_type": "package", "sha256": "54f90d6b6745fbb89eb7a1d36252862cda4ca111a945930adf6c3c927e17a1e8", "unvendored_tests": false, "version": "5.1.1"}, "demes": {"depends": ["attrs", "ruamel.yaml"], "file_name": "demes-0.2.3-py3-none-any.whl", "imports": ["demes"], "install_dir": "site", "name": "demes", "package_type": "package", "sha256": "aee96e88ff3af0031e0b08fd7178137712f404ee79d018fa22da70ee1eb9afb8", "unvendored_tests": false, "version": "0.2.3"}, "deprecation": {"depends": ["packaging"], "file_name": "deprecation-2.1.0-py2.py3-none-any.whl", "imports": ["deprecation"], "install_dir": "site", "name": "deprecation", "package_type": "package", "sha256": "cbe0ed9f57a41763288d4c186a5724049f705fc41db175407834602015994c63", "unvendored_tests": false, "version": "2.1.0"}, "distlib": {"depends": [], "file_name": "distlib-0.3.8-py2.py3-none-any.whl", "imports": ["distlib"], "install_dir": "site", "name": "distlib", "package_type": "package", "sha256": "ff3261bf3cb0a0101cee422b7ed068c493195452eee65b4a2dba235ba0690084", "unvendored_tests": false, "version": "0.3.8"}, "distro": {"depends": [], "file_name": "distro-1.9.0-py3-none-any.whl", "imports": ["distro"], "install_dir": "site", "name": "distro", "package_type": "package", "sha256": "697c8eca85a124c7448336dcfdcf6862c846a181e1c6430bc35120efffd24878", "unvendored_tests": false, "version": "1.9.0"}, "docutils": {"depends": [], "file_name": "docutils-0.21.1-py3-none-any.whl", "imports": ["docutils"], "install_dir": "site", "name": "docutils", "package_type": "package", "sha256": "87488ae4de9b3078ae4b1312a283999744b8c805ee724a6dfaec1ac629084dca", "unvendored_tests": false, "version": "0.21.1"}, "duckdb": {"depends": [], "file_name": "duckdb-1.1.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["duckdb"], "install_dir": "site", "name": "duckdb", "package_type": "package", "sha256": "eb068fe5ea3d8be969a5f6a6d03fcc835749a5d2c60cd7ca3e3d0fb670d77c2f", "unvendored_tests": false, "version": "1.1.2"}, "ewah-bool-utils": {"depends": [], "file_name": "ewah_bool_utils-1.2.2-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["ewah_bool_utils"], "install_dir": "site", "name": "ewah_bool_utils", "package_type": "package", "sha256": "e03c418e886c0e1eb7880056c8aae7da0ef42048aadfffe50275ff3fe517cf3f", "unvendored_tests": true, "version": "1.2.2"}, "ewah-bool-utils-tests": {"depends": ["ewah_bool_utils"], "file_name": "ewah-bool-utils-tests.tar", "imports": [], "install_dir": "site", "name": "ewah_bool_utils-tests", "package_type": "package", "sha256": "3dd445a828362da0931cb49cb0e3e89d91a1a98ae88f9350c1874edee8935fc5", "unvendored_tests": false, "version": "1.2.2"}, "exceptiongroup": {"depends": [], "file_name": "exceptiongroup-1.2.1-py3-none-any.whl", "imports": ["exceptiongroup"], "install_dir": "site", "name": "exceptiongroup", "package_type": "package", "sha256": "7137d0f93bb14003e13e0563a5ca817f4227aa7e57547a09afaf38b734ec106d", "unvendored_tests": false, "version": "1.2.1"}, "executing": {"depends": [], "file_name": "executing-2.0.1-py2.py3-none-any.whl", "imports": ["executing"], "install_dir": "site", "name": "executing", "package_type": "package", "sha256": "cfc677eb1eae5a13a502f324450ff9227bf66ad21ed83221e865f280b88dd09b", "unvendored_tests": false, "version": "2.0.1"}, "fastparquet": {"depends": ["cramjam", "numpy", "pandas", "fsspec", "packaging"], "file_name": "fastparquet-2024.5.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["fastparquet"], "install_dir": "site", "name": "fastparquet", "package_type": "package", "sha256": "b3d043c0d82ef617e86883cec008d57cec6ea0fdf2686b467d621ed75c4592b5", "unvendored_tests": false, "version": "2024.5.0"}, "fiona": {"depends": ["attrs", "certifi", "setuptools", "six", "click", "cligj"], "file_name": "fiona-1.9.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["fiona"], "install_dir": "site", "name": "fiona", "package_type": "package", "sha256": "2fb8e9f23bad7564e65af26ccfcce829764da5a0480f72f4b59994ed39f69153", "unvendored_tests": false, "version": "1.9.5"}, "fonttools": {"depends": [], "file_name": "fonttools-4.51.0-py3-none-any.whl", "imports": ["fontTools"], "install_dir": "site", "name": "fonttools", "package_type": "package", "sha256": "0c9a46b9246333de02d0fe02c0580840d7136744121f256a870218ed7482b607", "unvendored_tests": false, "version": "4.51.0"}, "fpcast-test": {"depends": [], "file_name": "fpcast_test-0.1.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["fpcast_test"], "install_dir": "site", "name": "fpcast-test", "package_type": "package", "sha256": "091e8dc5617759c6a952612d7d593335f98b0f590fc3146aed10634100801554", "unvendored_tests": false, "version": "0.1.1"}, "freesasa": {"depends": [], "file_name": "freesasa-2.2.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["freesasa"], "install_dir": "site", "name": "freesasa", "package_type": "package", "sha256": "b5145a96fede0cfb3ac87569a7038e86447303c3b51450ee2e889d157401ef8e", "unvendored_tests": false, "version": "2.2.1"}, "frozenlist": {"depends": [], "file_name": "frozenlist-1.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["frozenlist"], "install_dir": "site", "name": "frozenlist", "package_type": "package", "sha256": "d53912a6038aef81b1623921908827a839ec4eafa0ed0b6fa8d45e5d17d827df", "unvendored_tests": false, "version": "1.4.1"}, "fsspec": {"depends": [], "file_name": "fsspec-2025.3.2-py3-none-any.whl", "imports": ["fsspec"], "install_dir": "site", "name": "fsspec", "package_type": "package", "sha256": "977bc3c67514d3a4d579cfc993cff0e95cb37f309159b6cfc290f58618498f95", "unvendored_tests": true, "version": "2025.3.2"}, "fsspec-tests": {"depends": ["fsspec"], "file_name": "fsspec-tests.tar", "imports": [], "install_dir": "site", "name": "fsspec-tests", "package_type": "package", "sha256": "8f277582d07e3573ccb6058042cd29d2d07058ad1b82263a4df30321b6b34459", "unvendored_tests": false, "version": "2025.3.2"}, "future": {"depends": [], "file_name": "future-1.0.0-py3-none-any.whl", "imports": ["future"], "install_dir": "site", "name": "future", "package_type": "package", "sha256": "7e2c052f9ef30fba5ef92bfcf7b52d5b755dd778bd312dd0bf9ba3eb9884fbf9", "unvendored_tests": true, "version": "1.0.0"}, "future-tests": {"depends": ["future"], "file_name": "future-tests.tar", "imports": [], "install_dir": "site", "name": "future-tests", "package_type": "package", "sha256": "e2b2b6337da4048844588ae46edb457e3be12fde3711e1c5d273410d3f5c2b25", "unvendored_tests": false, "version": "1.0.0"}, "galpy": {"depends": ["numpy", "scipy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "astropy", "future", "setuptools"], "file_name": "galpy-1.10.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["galpy"], "install_dir": "site", "name": "galpy", "package_type": "package", "sha256": "dfd8bc97951b7b5f2745231245741e77c4cbebec8caacc386b307db5350d1d2d", "unvendored_tests": false, "version": "1.10.1"}, "gdal": {"depends": ["geos"], "file_name": "gdal-3.8.3.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "gdal", "package_type": "shared_library", "sha256": "cada0402f7c2dbe56c88bbf095a2847b07e35d6612836313a451adeb3ab83eac", "unvendored_tests": false, "version": "3.8.3"}, "gensim": {"depends": ["numpy", "scipy", "six", "smart-open", "wrapt"], "file_name": "gensim-4.3.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["gensim"], "install_dir": "site", "name": "gensim", "package_type": "package", "sha256": "63068eafcf08fb80eb6568221fedce54ded2c21d1401b59e00dcd6e7e31cb159", "unvendored_tests": true, "version": "4.3.3"}, "gensim-tests": {"depends": ["gensim"], "file_name": "gensim-tests.tar", "imports": [], "install_dir": "site", "name": "gensim-tests", "package_type": "package", "sha256": "cf4747e0a517fec2a2384c9820b8627d8e587d72f78f9a7524595996740ac31a", "unvendored_tests": false, "version": "4.3.3"}, "geopandas": {"depends": ["shapely", "fiona", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "packaging", "pandas"], "file_name": "geopandas-1.0.1-py3-none-any.whl", "imports": ["geopandas"], "install_dir": "site", "name": "geopandas", "package_type": "package", "sha256": "4d60379b3681d1ff6684ddabfa14b959587a577be1b74caa436153614e59671f", "unvendored_tests": true, "version": "1.0.1"}, "geopandas-tests": {"depends": ["geopandas"], "file_name": "geopandas-tests.tar", "imports": [], "install_dir": "site", "name": "geopandas-tests", "package_type": "package", "sha256": "19d11975add31835b37a921de900b8e7e2bb46b20423040bb574dd794692b1fb", "unvendored_tests": false, "version": "1.0.1"}, "geos": {"depends": [], "file_name": "geos-3.12.1.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "geos", "package_type": "shared_library", "sha256": "64400007fd90cac91e3d492a989734b09000ce5864403b91e4a6ffcd71900afa", "unvendored_tests": false, "version": "3.12.1"}, "gmpy2": {"depends": [], "file_name": "gmpy2-2.1.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["gmpy2"], "install_dir": "site", "name": "gmpy2", "package_type": "package", "sha256": "ebb4d157d170bf7b1e491afd4a38bc7b0c7d65c1bab0ee96d9356cde26543516", "unvendored_tests": false, "version": "2.1.5"}, "gsw": {"depends": ["numpy"], "file_name": "gsw-3.6.19-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["gsw"], "install_dir": "site", "name": "gsw", "package_type": "package", "sha256": "146bf94684e7f06b03d9641f11df2f540f45494e8052df42a584ecc163d2a9e7", "unvendored_tests": true, "version": "3.6.19"}, "gsw-tests": {"depends": ["gsw"], "file_name": "gsw-tests.tar", "imports": [], "install_dir": "site", "name": "gsw-tests", "package_type": "package", "sha256": "f5a93665eeed1120350142cebb8b09ef6dbfbd8387defa9b98ffb0811c0a6c2a", "unvendored_tests": false, "version": "3.6.19"}, "h11": {"depends": [], "file_name": "h11-0.14.0-py3-none-any.whl", "imports": ["h11"], "install_dir": "site", "name": "h11", "package_type": "package", "sha256": "179511e1878e1e4fb6739c944b7c653403cee7334041c6870f790a9f8029991a", "unvendored_tests": true, "version": "0.14.0"}, "h11-tests": {"depends": ["h11"], "file_name": "h11-tests.tar", "imports": [], "install_dir": "site", "name": "h11-tests", "package_type": "package", "sha256": "fd911473c62b2e4cc0bf33ea0a3e055b1c620db93c5745ccb519b5cf803da378", "unvendored_tests": false, "version": "0.14.0"}, "h3": {"depends": [], "file_name": "h3-4.2.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["h3"], "install_dir": "site", "name": "h3", "package_type": "package", "sha256": "d4e7270d91c942ed2f8124c839addad3cc1c7419cf3a661856680a25bb8da99b", "unvendored_tests": false, "version": "4.2.1"}, "h5py": {"depends": ["numpy", "pkgconfig"], "file_name": "h5py-3.12.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["h5py"], "install_dir": "site", "name": "h5py", "package_type": "package", "sha256": "a56b66beb6c8708b128299e414fc9a1d646b1a432e04822059a7c3fcfaaf9e10", "unvendored_tests": true, "version": "3.12.1"}, "h5py-tests": {"depends": ["h5py"], "file_name": "h5py-tests.tar", "imports": [], "install_dir": "site", "name": "h5py-tests", "package_type": "package", "sha256": "3bf38920307fa0f08368d49694bfde6e9f56318080a7a74a553f8a27aa42bcf0", "unvendored_tests": false, "version": "3.12.1"}, "hashlib": {"depends": ["openssl"], "file_name": "hashlib-1.0.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["_hashlib"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>", "package_type": "cpython_module", "sha256": "4a6971a70bf547ad93f011e121392803ce1595e62e8b3ca719ffa3ec91384722", "unvendored_tests": false, "version": "1.0.0"}, "html5lib": {"depends": ["webencodings", "six"], "file_name": "html5lib-1.1-py2.py3-none-any.whl", "imports": ["html5lib"], "install_dir": "site", "name": "html5lib", "package_type": "package", "sha256": "8faa3cdcfaf78900c25c50552cae5ddea0898d985d676f4efb163e319975f6e7", "unvendored_tests": false, "version": "1.1"}, "httpcore": {"depends": ["certifi", "h11", "ssl"], "file_name": "httpcore-1.0.7-py3-none-any.whl", "imports": ["httpcore"], "install_dir": "site", "name": "httpcore", "package_type": "package", "sha256": "2409f092fbd9ae88a1e31899e1e2e97c5955fa3fac22a5f1765c2b546b1a581e", "unvendored_tests": false, "version": "1.0.7"}, "httpx": {"depends": [], "file_name": "httpx-0.28.1-py3-none-any.whl", "imports": ["httpx"], "install_dir": "site", "name": "httpx", "package_type": "package", "sha256": "4ca06d11b8dc47f96ec55f5ad601ea95a22d34cde012af826d2ba7640f051a7c", "unvendored_tests": false, "version": "0.28.1"}, "idna": {"depends": [], "file_name": "idna-3.7-py3-none-any.whl", "imports": ["idna"], "install_dir": "site", "name": "idna", "package_type": "package", "sha256": "9d4685891e3e37434e09b1becda7e96a284e660c7aea9222564d88b6c3527c09", "unvendored_tests": false, "version": "3.7"}, "igraph": {"depends": ["texttable"], "file_name": "igraph-0.11.4-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["igraph"], "install_dir": "site", "name": "igraph", "package_type": "package", "sha256": "9f6bd011e677db3f84e573f24590b774f5d1fd8b92af92d94b163d716f582bf3", "unvendored_tests": false, "version": "0.11.4"}, "imageio": {"depends": ["numpy", "pillow"], "file_name": "imageio-2.36.0-py3-none-any.whl", "imports": ["imageio"], "install_dir": "site", "name": "imageio", "package_type": "package", "sha256": "f87ca5f6fc8c965ac35c9d9e716c4691f87923088b1f53a5a09d67f2c6c3d6e6", "unvendored_tests": false, "version": "2.36.0"}, "iminuit": {"depends": ["numpy"], "file_name": "iminuit-2.30.1-cp312-cp312-pyo<PERSON><PERSON>_2024_0_wasm32.whl", "imports": ["iminuit"], "install_dir": "site", "name": "iminuit", "package_type": "package", "sha256": "4a13e54d477ef824d4e94fc2c4acf52a1d795c58596970e4b1cc9e52882759bc", "unvendored_tests": false, "version": "2.30.1"}, "iniconfig": {"depends": [], "file_name": "iniconfig-2.0.0-py3-none-any.whl", "imports": ["iniconfig"], "install_dir": "site", "name": "iniconfig", "package_type": "package", "sha256": "92ecd073d77c7da9cd53c8f4fdd46c1c2562c082ece2c9f23b71d7ad0f2a89af", "unvendored_tests": false, "version": "2.0.0"}, "ipython": {"depends": ["asttokens", "decorator", "executing", "matplotlib-inline", "prompt_toolkit", "pure-eval", "pygments", "six", "stack-data", "traitlets", "sqlite3", "wcwidth"], "file_name": "ipython-8.23.0-py3-none-any.whl", "imports": ["IPython"], "install_dir": "site", "name": "ipython", "package_type": "package", "sha256": "af05617e0f9bb869649a662b33081d0d6f3de017b50f425b3acf5c4da3a28cd2", "unvendored_tests": true, "version": "8.23.0"}, "ipython-tests": {"depends": ["ipython"], "file_name": "ipython-tests.tar", "imports": [], "install_dir": "site", "name": "ipython-tests", "package_type": "package", "sha256": "7fb4c87c339f28ae5335fd5a0c1836f63cfdca379175ea335c61cadff60427fd", "unvendored_tests": false, "version": "8.23.0"}, "jedi": {"depends": ["parso"], "file_name": "jedi-0.19.1-py2.py3-none-any.whl", "imports": ["jedi"], "install_dir": "site", "name": "jedi", "package_type": "package", "sha256": "31edb344d473e145a029392523e637386bfc6a1977b22e2305e034c96eb27b2a", "unvendored_tests": true, "version": "0.19.1"}, "jedi-tests": {"depends": ["jedi"], "file_name": "jedi-tests.tar", "imports": [], "install_dir": "site", "name": "jedi-tests", "package_type": "package", "sha256": "c23da9850f5f5d71e132ed8c28100be3d203ddd7f01feec02b4b6c173f6737e9", "unvendored_tests": false, "version": "0.19.1"}, "jinja2": {"depends": ["markupsafe"], "file_name": "Jinja2-3.1.3-py3-none-any.whl", "imports": ["jinja2"], "install_dir": "site", "name": "Jinja2", "package_type": "package", "sha256": "2c74f33712082b6807f96c6ad4466fe57307d79659887078952f1fa6572580b7", "unvendored_tests": false, "version": "3.1.3"}, "jiter": {"depends": [], "file_name": "jiter-0.8.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["jiter"], "install_dir": "site", "name": "jiter", "package_type": "package", "sha256": "ff0beeb857eedae373da0be5798f86ee34699ce6136afa439d0355f19ebdacd0", "unvendored_tests": false, "version": "0.8.2"}, "joblib": {"depends": [], "file_name": "joblib-1.4.0-py3-none-any.whl", "imports": ["joblib"], "install_dir": "site", "name": "joblib", "package_type": "package", "sha256": "c4a3bb8273356564230296537aae1c175e8fcc6bd998af6b9ab1be74e2d141b6", "unvendored_tests": true, "version": "1.4.0"}, "joblib-tests": {"depends": ["joblib"], "file_name": "joblib-tests.tar", "imports": [], "install_dir": "site", "name": "joblib-tests", "package_type": "package", "sha256": "aa5c52a8673fc23d6f569cfa538ff0e9bff60221ddc09aaf70509ad307bc235f", "unvendored_tests": false, "version": "1.4.0"}, "jsonschema": {"depends": ["attrs", "pyrsistent", "referencing", "jsonschema_specifications"], "file_name": "jsonschema-4.21.1-py3-none-any.whl", "imports": ["jsonschema"], "install_dir": "site", "name": "jsonschema", "package_type": "package", "sha256": "0a9f52c1cf0dab8ee5c0f5b63b08cd3a588c6192f4e22739caaed4f004676409", "unvendored_tests": true, "version": "4.21.1"}, "jsonschema-specifications": {"depends": [], "file_name": "jsonschema_specifications-2023.12.1-py3-none-any.whl", "imports": ["jsonschema_specifications"], "install_dir": "site", "name": "jsonschema_specifications", "package_type": "package", "sha256": "8d3299fdb995a6e68a78b6882e110b20dce641f7c6896bb4da4c7237c476a515", "unvendored_tests": true, "version": "2023.12.1"}, "jsonschema-specifications-tests": {"depends": ["jsonschema_specifications"], "file_name": "jsonschema-specifications-tests.tar", "imports": [], "install_dir": "site", "name": "jsonschema_specifications-tests", "package_type": "package", "sha256": "b253891ed105b4a530508915b8741be209160bb88d4b0d263d94b3424a826543", "unvendored_tests": false, "version": "2023.12.1"}, "jsonschema-tests": {"depends": ["jsonschema"], "file_name": "jsonschema-tests.tar", "imports": [], "install_dir": "site", "name": "jsonschema-tests", "package_type": "package", "sha256": "be754ae8b11e979de484760b6c55c9fbc65af439500133e6f70342368274561c", "unvendored_tests": false, "version": "4.21.1"}, "kiwisolver": {"depends": [], "file_name": "kiwisolver-1.4.5-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["kiwisolver"], "install_dir": "site", "name": "kiwisolver", "package_type": "package", "sha256": "d7652d88b3390d9d1d0175d6c79da675b42bac2cd748cc9b5766a3235da4b762", "unvendored_tests": false, "version": "1.4.5"}, "lakers-python": {"depends": [], "file_name": "lakers_python-0.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["lakers"], "install_dir": "site", "name": "lakers-python", "package_type": "package", "sha256": "58f5612fc9fa82debb839ca2fff8625019057eca09872f9a8b9203ba8007febe", "unvendored_tests": false, "version": "0.4.1"}, "lazy-loader": {"depends": [], "file_name": "lazy_loader-0.4-py3-none-any.whl", "imports": ["lazy_loader"], "install_dir": "site", "name": "lazy_loader", "package_type": "package", "sha256": "b2d18752ff3fed4cd3cf7c23a88f84d9b87055c80eae2689ffe809cf766b763a", "unvendored_tests": true, "version": "0.4"}, "lazy-loader-tests": {"depends": ["lazy_loader"], "file_name": "lazy-loader-tests.tar", "imports": [], "install_dir": "site", "name": "lazy_loader-tests", "package_type": "package", "sha256": "d84c83c43ecdb504e58e7df246625117191ec84094e5ccf43867d8e7e7adf8ad", "unvendored_tests": false, "version": "0.4"}, "lazy-object-proxy": {"depends": [], "file_name": "lazy_object_proxy-1.10.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["lazy_object_proxy"], "install_dir": "site", "name": "lazy-object-proxy", "package_type": "package", "sha256": "2d8ca147965162cb0621906b5181b2589adabf2d1deebfb99d97b3bddf1bf118", "unvendored_tests": false, "version": "1.10.0"}, "libcst": {"depends": ["pyyaml"], "file_name": "libcst-1.4.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["libcst"], "install_dir": "site", "name": "libcst", "package_type": "package", "sha256": "5118f345836eb6c09ddf0e20c36fcd6de26d2b2e2f4bee380dab55ec36faa234", "unvendored_tests": true, "version": "1.4.0"}, "libcst-tests": {"depends": ["libcst"], "file_name": "libcst-tests.tar", "imports": [], "install_dir": "site", "name": "libcst-tests", "package_type": "package", "sha256": "f4bad6fc8af8a50e7178d4d2f3927f18abcb52c00546edde62f8c40c3ddd7d8c", "unvendored_tests": false, "version": "1.4.0"}, "libhdf5": {"depends": [], "file_name": "libhdf5-1.12.1.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "libhdf5", "package_type": "shared_library", "sha256": "fe57d527663650699e65ee35c98072e13085eccf231bedb489388c1ca45141b4", "unvendored_tests": false, "version": "1.12.1"}, "libheif": {"depends": [], "file_name": "libheif-1.12.0.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "lib<PERSON><PERSON>", "package_type": "shared_library", "sha256": "a24e7af8a123ecad7d446820486fb382745a522c4b48fcad340e11156e0cc92a", "unvendored_tests": false, "version": "1.12.0"}, "libmagic": {"depends": [], "file_name": "libmagic-5.42.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "libmagic", "package_type": "shared_library", "sha256": "604ecf7fc42917b1c0c0c920ae0629427c48359b9afd25d928487bcf4ac44f83", "unvendored_tests": false, "version": "5.42"}, "lightgbm": {"depends": ["numpy", "scipy", "scikit-learn"], "file_name": "lightgbm-4.5.0-py3-none-pyodide_2024_0_wasm32.whl", "imports": ["lightgbm"], "install_dir": "site", "name": "lightgbm", "package_type": "package", "sha256": "2f24d464868ce07ef80061632c011f7f8e2564179cf6b781f403fd67732d456c", "unvendored_tests": false, "version": "4.5.0"}, "logbook": {"depends": ["ssl"], "file_name": "logbook-1.7.0.post0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["logbook"], "install_dir": "site", "name": "logbook", "package_type": "package", "sha256": "bfe651c3fe67842b543c34e3a0b3d42e9df3aa49e50f377bf9aa3362e7a8d80e", "unvendored_tests": false, "version": "1.7.0.post0"}, "lxml": {"depends": [], "file_name": "lxml-5.2.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["lxml"], "install_dir": "site", "name": "lxml", "package_type": "package", "sha256": "7213dd8b1c2bd24e37356d74454e463103b86bb5ff7dfaf1bd1794d8ff7ddc02", "unvendored_tests": false, "version": "5.2.1"}, "lzma": {"depends": [], "file_name": "lzma-1.0.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["lzma", "_lzma"], "install_dir": "site", "name": "lzma", "package_type": "cpython_module", "sha256": "51b77233960d4353deee46bd23b39ebfd53039835693f7dc2fa8858c88e8eee8", "unvendored_tests": false, "version": "1.0.0"}, "markupsafe": {"depends": [], "file_name": "markupsafe-2.1.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["markupsafe"], "install_dir": "site", "name": "MarkupSafe", "package_type": "package", "sha256": "cceab881a7705a8e219f480ed089c4f5b87aad34d704e49360a53173e83f2681", "unvendored_tests": false, "version": "2.1.5"}, "matplotlib": {"depends": ["contourpy", "cycler", "fonttools", "kiwisolver", "numpy", "packaging", "pillow", "pyparsing", "python-dateutil", "pytz", "matplotlib-pyodide"], "file_name": "matplotlib-3.8.4-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pylab", "mpl_toolkits", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "50d4641b32e84ecb0a2c271728d4be73b1e0a0f5f29aa7dd6ea280f888b91340", "unvendored_tests": true, "version": "3.8.4"}, "matplotlib-inline": {"depends": ["traitlets"], "file_name": "matplotlib_inline-0.1.7-py3-none-any.whl", "imports": ["matplotlib-inline"], "install_dir": "site", "name": "matplotlib-inline", "package_type": "package", "sha256": "79a482aa6f83e758b6a60dae955c915682e9706bb8712e11a909a6eb7b0d1f2d", "unvendored_tests": false, "version": "0.1.7"}, "matplotlib-pyodide": {"depends": [], "file_name": "matplotlib_pyodide-0.2.3-py3-none-any.whl", "imports": ["matplotlib_pyodide"], "install_dir": "site", "name": "matplotlib-pyodide", "package_type": "package", "sha256": "070d6244e7ae44f8753d3a78cf5c30f93c8d781e620991af848dcfd44112839f", "unvendored_tests": false, "version": "0.2.3"}, "matplotlib-tests": {"depends": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "matplotlib-tests.tar", "imports": [], "install_dir": "site", "name": "matplotlib-tests", "package_type": "package", "sha256": "f44d1d465da988ee9abed18c7aee1f6a8a551290e78fa744193125c5ceea9f83", "unvendored_tests": false, "version": "3.8.4"}, "memory-allocator": {"depends": [], "file_name": "memory_allocator-0.1.4-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["memory_allocator"], "install_dir": "site", "name": "memory-allocator", "package_type": "package", "sha256": "ded1d0f51d0c298d5362fcffd033374d97122e6b0a2570a75d15b99dd626ca3d", "unvendored_tests": false, "version": "0.1.4"}, "micropip": {"depends": ["packaging"], "file_name": "micropip-0.9.0-py3-none-any.whl", "imports": ["micropip"], "install_dir": "site", "name": "micropip", "package_type": "package", "sha256": "034f22763607744f982d2911170c50b496a38b8ba0535e5a09618475b1d7b051", "unvendored_tests": false, "version": "0.9.0"}, "mmh3": {"depends": [], "file_name": "mmh3-4.1.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["mmh3"], "install_dir": "site", "name": "mmh3", "package_type": "package", "sha256": "7099904077a0dd337a0b0a0ba0316d9bd80fff4046444d9b24171e4ae24cc898", "unvendored_tests": false, "version": "4.1.0"}, "mne": {"depends": ["numpy", "scipy", "setuptools", "decorator", "lazy_loader", "packaging"], "file_name": "mne-1.8.0-py3-none-any.whl", "imports": ["mne"], "install_dir": "site", "name": "mne", "package_type": "package", "sha256": "783b45078798cc4f203aca82acf43353b3f738238c4906b45810b6931c28ac36", "unvendored_tests": true, "version": "1.8.0"}, "mne-tests": {"depends": ["mne"], "file_name": "mne-tests.tar", "imports": [], "install_dir": "site", "name": "mne-tests", "package_type": "package", "sha256": "1c93bd5f37ffb7c7fe3048c3db5694c767d55352c4d0cc91bd522ca52cc6ae03", "unvendored_tests": false, "version": "1.8.0"}, "more-itertools": {"depends": [], "file_name": "more_itertools-10.2.0-py3-none-any.whl", "imports": ["more_itertools"], "install_dir": "site", "name": "more-itertools", "package_type": "package", "sha256": "4dab4e88bb083f1c70bf48adca5098af88c0d59c87f4328bf878005c0949c6d7", "unvendored_tests": false, "version": "10.2.0"}, "mpmath": {"depends": [], "file_name": "mpmath-1.3.0-py3-none-any.whl", "imports": ["mpmath"], "install_dir": "site", "name": "mpmath", "package_type": "package", "sha256": "5c8b3c27ea61c54b90c56a5adf9ddb07d7c3591b80e9f83d4f03009bba6f5ac5", "unvendored_tests": true, "version": "1.3.0"}, "mpmath-tests": {"depends": ["mpmath"], "file_name": "mpmath-tests.tar", "imports": [], "install_dir": "site", "name": "mpmath-tests", "package_type": "package", "sha256": "e674fa6bd8810224d96eaa05197bfff61286f32b45ee2ad38ffda2b06b628a68", "unvendored_tests": false, "version": "1.3.0"}, "msgpack": {"depends": [], "file_name": "msgpack-1.1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["msgpack"], "install_dir": "site", "name": "msgpack", "package_type": "package", "sha256": "5eef122129d4f45f81a766989b0b7e93717188d04c3d8207cc0aa51f40942e7e", "unvendored_tests": false, "version": "1.1.0"}, "msgspec": {"depends": [], "file_name": "msgspec-0.18.6-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["msgspec"], "install_dir": "site", "name": "msgspec", "package_type": "package", "sha256": "897312e6a62138495162a039881bb90cd743777e7ea79e8ad22bb4ea4f1dd319", "unvendored_tests": false, "version": "0.18.6"}, "msprime": {"depends": ["numpy", "newick", "tskit", "demes", "rpds-py"], "file_name": "msprime-1.3.3-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["msprime"], "install_dir": "site", "name": "msprime", "package_type": "package", "sha256": "0fff280692699e1f7ed9e31352f2571c7efb20183cbee5319fc6438597f66007", "unvendored_tests": false, "version": "1.3.3"}, "multidict": {"depends": [], "file_name": "multidict-6.0.5-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["multidict"], "install_dir": "site", "name": "multidict", "package_type": "package", "sha256": "27bf80a8593b18b154f97b1e3d1874503f03d4e23ba7c97defdeb896aa489286", "unvendored_tests": false, "version": "6.0.5"}, "munch": {"depends": ["setuptools", "six"], "file_name": "munch-4.0.0-py2.py3-none-any.whl", "imports": ["munch"], "install_dir": "site", "name": "munch", "package_type": "package", "sha256": "7117c5b44033ad085870f479a2c019f881570f245223447c9627893f971438e7", "unvendored_tests": false, "version": "4.0.0"}, "mypy": {"depends": [], "file_name": "mypy-1.9.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["mypyc", "mypy"], "install_dir": "site", "name": "mypy", "package_type": "package", "sha256": "7e41f1ee7b77a91477788db77b17dc1cbe1342c3f6732fbcb5cc84d9e6962292", "unvendored_tests": true, "version": "1.9.0"}, "mypy-tests": {"depends": ["mypy"], "file_name": "mypy-tests.tar", "imports": [], "install_dir": "site", "name": "mypy-tests", "package_type": "package", "sha256": "03d347cb7e03befd6bbb44b71759f5177b855204d7632afa774e92f25ce113b2", "unvendored_tests": false, "version": "1.9.0"}, "narwhals": {"depends": [], "file_name": "narwhals-1.41.0-py3-none-any.whl", "imports": ["narwhals"], "install_dir": "site", "name": "narwhals", "package_type": "package", "sha256": "030d0f488d4b5b1f6497e43a12810e304f3bb994075b819338c15fe1cfce9f8a", "unvendored_tests": false, "version": "1.41.0"}, "netcdf4": {"depends": ["numpy", "packaging", "h5py", "cftime", "certifi"], "file_name": "netcdf4-1.7.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["netCDF4"], "install_dir": "site", "name": "netcdf4", "package_type": "package", "sha256": "8f552958adb12626ede6605974a6a9ee3bb225e16ea798eaa33502a04498b661", "unvendored_tests": false, "version": "1.7.2"}, "networkx": {"depends": ["decorator", "setuptools", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy"], "file_name": "networkx-3.4.2-py3-none-any.whl", "imports": ["networkx"], "install_dir": "site", "name": "networkx", "package_type": "package", "sha256": "fc86ae678fdbab25783d543290dfd2fea8f9cc5161cdb4e61ce2687de57665bb", "unvendored_tests": true, "version": "3.4.2"}, "networkx-tests": {"depends": ["networkx"], "file_name": "networkx-tests.tar", "imports": [], "install_dir": "site", "name": "networkx-tests", "package_type": "package", "sha256": "41cd06690fc581bb786b93c2ea701d0fe399f048bb4ba95fb2cc182cef863555", "unvendored_tests": false, "version": "3.4.2"}, "newick": {"depends": [], "file_name": "newick-1.9.0-py2.py3-none-any.whl", "imports": ["newick"], "install_dir": "site", "name": "newick", "package_type": "package", "sha256": "0c36857981135c2fdab3c5014d2c166686f3da63a77d4d7ade1dacc5624f1e3f", "unvendored_tests": false, "version": "1.9.0"}, "nh3": {"depends": [], "file_name": "nh3-0.2.17-cp37-abi3-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["nh3"], "install_dir": "site", "name": "nh3", "package_type": "package", "sha256": "751f00fd5909f75ffb9d7a0efe5b0cd1eee888bcd767debe5079e5cd5eef2f22", "unvendored_tests": false, "version": "0.2.17"}, "nlopt": {"depends": ["numpy"], "file_name": "nlopt-2.9.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["nlopt"], "install_dir": "site", "name": "nlopt", "package_type": "package", "sha256": "cdfaf6037286471e7759f7f9f0e4c182156f536d55570258ac3ec1774fabad82", "unvendored_tests": false, "version": "2.9.1"}, "nltk": {"depends": ["regex", "sqlite3"], "file_name": "nltk-3.8.1-py3-none-any.whl", "imports": ["nltk"], "install_dir": "site", "name": "nltk", "package_type": "package", "sha256": "73310c9208d65ccc7967d38f1564c478bd9478945c238707d66c6a1797153b77", "unvendored_tests": true, "version": "3.8.1"}, "nltk-tests": {"depends": ["nltk"], "file_name": "nltk-tests.tar", "imports": [], "install_dir": "site", "name": "nltk-tests", "package_type": "package", "sha256": "f027e65392bd6527591ae6ec94f6ad364764c6f77beab2def79318afedd6a050", "unvendored_tests": false, "version": "3.8.1"}, "numcodecs": {"depends": ["numpy", "msgpack"], "file_name": "numcodecs-0.13.1-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["numcodecs"], "install_dir": "site", "name": "numcodecs", "package_type": "package", "sha256": "63591f47fcf2b76a28b934245d69fff2a050a9aa0df32e1392e9c080dcf4dc9e", "unvendored_tests": true, "version": "0.13.1"}, "numcodecs-tests": {"depends": ["numcodecs"], "file_name": "numcodecs-tests.tar", "imports": [], "install_dir": "site", "name": "numcodecs-tests", "package_type": "package", "sha256": "7afdda6cbdf4bd6e7f46d7f3af0811c63e2955c02de81d8a8a766c6e9efdb3a1", "unvendored_tests": false, "version": "0.13.1"}, "numpy": {"depends": [], "file_name": "numpy-2.0.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["numpy"], "install_dir": "site", "name": "numpy", "package_type": "package", "sha256": "ebb61241d962b98b21597d7ce43b67668a6d5bab3acec4fbe958657320d7cd08", "unvendored_tests": true, "version": "2.0.2"}, "numpy-tests": {"depends": ["numpy"], "file_name": "numpy-tests.tar", "imports": [], "install_dir": "site", "name": "numpy-tests", "package_type": "package", "sha256": "cad7ee071528979cc444c57a20a6996f50d441a904709b41d8a5f70177a9026e", "unvendored_tests": false, "version": "2.0.2"}, "openai": {"depends": ["httpx", "pydantic", "typing-extensions", "distro", "anyio", "jiter"], "file_name": "openai-1.68.2-py3-none-any.whl", "imports": ["openai"], "install_dir": "site", "name": "openai", "package_type": "package", "sha256": "508d5d5ee0a39d1ef108e09888af784c54a639f48ebb354517e3d52d40900f22", "unvendored_tests": false, "version": "1.68.2"}, "openblas": {"depends": [], "file_name": "openblas-0.3.26.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "openblas", "package_type": "shared_library", "sha256": "787ea9a8a5cd160204388d03324b3b7ce5c9aea14b5680cec987f837e76c5198", "unvendored_tests": false, "version": "0.3.26"}, "opencv-python": {"depends": ["numpy"], "file_name": "opencv_python-*********-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["cv2"], "install_dir": "site", "name": "opencv-python", "package_type": "package", "sha256": "fae5731bfaa3590eefd92b70fb78a090882031f31905451230b2804e3d576800", "unvendored_tests": false, "version": "*********"}, "openssl": {"depends": [], "file_name": "openssl-1.1.1w.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "openssl", "package_type": "shared_library", "sha256": "20b92cd9b9adb1a739af9f93772b60c084203aacf63ae1b2353951bd80c97f08", "unvendored_tests": false, "version": "1.1.1w"}, "optlang": {"depends": ["sympy", "six", "swiglpk"], "file_name": "optlang-1.8.1-py2.py3-none-any.whl", "imports": ["optlang"], "install_dir": "site", "name": "optlang", "package_type": "package", "sha256": "b45dd43fb6383a3769ad63ef05316d9c07cdb1cf82baee03a25c7e0bb42835e4", "unvendored_tests": true, "version": "1.8.1"}, "optlang-tests": {"depends": ["optlang"], "file_name": "optlang-tests.tar", "imports": [], "install_dir": "site", "name": "optlang-tests", "package_type": "package", "sha256": "7ce13eb9c2abf339b588e362767f5b8dfe4d26a5f11635b539661c7877a40dd2", "unvendored_tests": false, "version": "1.8.1"}, "orjson": {"depends": [], "file_name": "orjson-3.10.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "d492fd640b39620ec1ffb89441d74119b35baf77f672226aa9ee05ebdb29ca9b", "unvendored_tests": false, "version": "3.10.1"}, "osqp": {"depends": ["jinja2", "numpy", "scipy"], "file_name": "osqp-1.0.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["osqp"], "install_dir": "site", "name": "osqp", "package_type": "package", "sha256": "bfed35d7a38f289d7d66ac43de2663db66e8ccaf0e1c1cb9b05af43a0558f0a3", "unvendored_tests": true, "version": "1.0.0"}, "osqp-tests": {"depends": ["osqp"], "file_name": "osqp-tests.tar", "imports": [], "install_dir": "site", "name": "osqp-tests", "package_type": "package", "sha256": "8f0b82951f52473c7c595f17ebf268403a86bc918ee7bba682e9aa6baa7af54d", "unvendored_tests": false, "version": "1.0.0"}, "packaging": {"depends": [], "file_name": "packaging-24.2-py3-none-any.whl", "imports": ["packaging"], "install_dir": "site", "name": "packaging", "package_type": "package", "sha256": "fbf6a5ace596eb8e28fe0089ecfc0bca2eb3930563e9ca06acb98ea5302b99f7", "unvendored_tests": false, "version": "24.2"}, "pandas": {"depends": ["numpy", "python-dateutil", "pytz"], "file_name": "pandas-2.2.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pandas"], "install_dir": "site", "name": "pandas", "package_type": "package", "sha256": "5502f0f94f93a482b851d0b8ca43be692c1b8d20215a168313656847ff74d63a", "unvendored_tests": true, "version": "2.2.3"}, "pandas-tests": {"depends": ["pandas"], "file_name": "pandas-tests.tar", "imports": [], "install_dir": "site", "name": "pandas-tests", "package_type": "package", "sha256": "3d39f671d7b544935b45b72b1af7a8187efb368fcf5910436a3a37baa364d294", "unvendored_tests": false, "version": "2.2.3"}, "parso": {"depends": [], "file_name": "parso-0.8.4-py2.py3-none-any.whl", "imports": ["parso"], "install_dir": "site", "name": "parso", "package_type": "package", "sha256": "c97dfaa7eb01ca1759f92ca0bedacdd9e5e95b29e026aa51f75ca1bf8e144685", "unvendored_tests": false, "version": "0.8.4"}, "patsy": {"depends": ["numpy", "six"], "file_name": "patsy-0.5.6-py2.py3-none-any.whl", "imports": ["patsy"], "install_dir": "site", "name": "patsy", "package_type": "package", "sha256": "e52b38b82b8e67a78d8f7b0e38a3a9a22ee16e029e853434e700e7acb617b67b", "unvendored_tests": true, "version": "0.5.6"}, "patsy-tests": {"depends": ["patsy"], "file_name": "patsy-tests.tar", "imports": [], "install_dir": "site", "name": "patsy-tests", "package_type": "package", "sha256": "2a2478cce831c97a9c83858774ac4915dab53446fb8b0444f344ebfcd042f902", "unvendored_tests": false, "version": "0.5.6"}, "pcodec": {"depends": ["numpy"], "file_name": "pcodec-0.3.3-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pcodec"], "install_dir": "site", "name": "pcodec", "package_type": "package", "sha256": "96adcbd07aaf76a8e3441545c45e6202293b14a18dadf24af7c2c3fc34991f56", "unvendored_tests": false, "version": "0.3.3"}, "peewee": {"depends": ["sqlite3", "cffi"], "file_name": "peewee-3.17.3-py3-none-any.whl", "imports": ["peewee"], "install_dir": "site", "name": "peewee", "package_type": "package", "sha256": "00e4a6949864d2872c234188c9193b030ab90c4da39d71dcf7320506a19b3536", "unvendored_tests": true, "version": "3.17.3"}, "peewee-tests": {"depends": ["peewee"], "file_name": "peewee-tests.tar", "imports": [], "install_dir": "site", "name": "peewee-tests", "package_type": "package", "sha256": "737c608df70103eb4d5e16482bd324b28f422512eacd3dea2f130d13bc4f47c1", "unvendored_tests": false, "version": "3.17.3"}, "pi-heif": {"depends": ["cffi", "pillow", "lib<PERSON><PERSON>"], "file_name": "pi_heif-0.21.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pi_heif"], "install_dir": "site", "name": "pi-heif", "package_type": "package", "sha256": "33372324d0d6c9a37bf67cbddf1ef24f96049ec357c96121063a2cc0295dc25c", "unvendored_tests": false, "version": "0.21.0"}, "pillow": {"depends": [], "file_name": "pillow-10.2.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["PIL"], "install_dir": "site", "name": "Pillow", "package_type": "package", "sha256": "43eab147a9355385a99eeac18fb577d4af2fe4bd0bbd23d3ba3ddb92e72c2815", "unvendored_tests": false, "version": "10.2.0"}, "pillow-heif": {"depends": ["cffi", "pillow", "lib<PERSON><PERSON>"], "file_name": "pillow_heif-0.20.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pillow_heif"], "install_dir": "site", "name": "pillow-heif", "package_type": "package", "sha256": "d7e1aba18a35a014915e567b33c5ba45ac4f9ea5f95c6e7b608677052e5e6cee", "unvendored_tests": false, "version": "0.20.0"}, "pkgconfig": {"depends": [], "file_name": "pkgconfig-1.5.5-py3-none-any.whl", "imports": ["pkgconfig"], "install_dir": "site", "name": "pkgconfig", "package_type": "package", "sha256": "6865593962300e910d9d125972b590bf4bac693f54a7ce9c8602625ae42212f8", "unvendored_tests": false, "version": "1.5.5"}, "pluggy": {"depends": [], "file_name": "pluggy-1.5.0-py3-none-any.whl", "imports": ["pluggy"], "install_dir": "site", "name": "pluggy", "package_type": "package", "sha256": "93a74d265d6ee7de8f70cdae64d5ec9d8e0e7d18f791778c1f0cd442ed61d71a", "unvendored_tests": false, "version": "1.5.0"}, "polars": {"depends": [], "file_name": "polars-1.18.0-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["polars"], "install_dir": "site", "name": "polars", "package_type": "package", "sha256": "2e1410d89042e0c05a00832a1e39c9fe0a89d786dd8a3b7a9324cdc1eef5097c", "unvendored_tests": false, "version": "1.18.0"}, "pplpy": {"depends": ["gmpy2", "cysignals"], "file_name": "pplpy-0.8.10-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["ppl"], "install_dir": "site", "name": "pplpy", "package_type": "package", "sha256": "d644230564c6629e9532da4b1e68483f830b8898561e52c998210aae95eb42c3", "unvendored_tests": false, "version": "0.8.10"}, "primecountpy": {"depends": ["cysignals"], "file_name": "primecountpy-0.1.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["primecountpy"], "install_dir": "site", "name": "primecountpy", "package_type": "package", "sha256": "33729e94b7e56fecbf70e290053f9c3be6addc9b396ca41917913ced8feb3f45", "unvendored_tests": false, "version": "0.1.0"}, "prompt-toolkit": {"depends": [], "file_name": "prompt_toolkit-3.0.43-py3-none-any.whl", "imports": ["prompt_toolkit"], "install_dir": "site", "name": "prompt_toolkit", "package_type": "package", "sha256": "3237bd2e78447aef7f9a3eddad92ca70410d0aaa1fe9f9797c788bfb3c6994c4", "unvendored_tests": false, "version": "3.0.43"}, "protobuf": {"depends": [], "file_name": "protobuf-5.29.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["google"], "install_dir": "site", "name": "protobuf", "package_type": "package", "sha256": "1c061201f6366b1638af5ec0f5f6a382a73d9773c08749650cdf0b4d656815bc", "unvendored_tests": false, "version": "5.29.2"}, "pure-eval": {"depends": [], "file_name": "pure_eval-0.2.3-py3-none-any.whl", "imports": ["pure_eval"], "install_dir": "site", "name": "pure-eval", "package_type": "package", "sha256": "ea9d0a2c5dc624585d95ea9dbde454d9eb26078ed7a2e6509f97927efdfc9fa8", "unvendored_tests": false, "version": "0.2.3"}, "py": {"depends": [], "file_name": "py-1.11.0-py2.py3-none-any.whl", "imports": ["py"], "install_dir": "site", "name": "py", "package_type": "package", "sha256": "9dfae43125fc30d0ca4d8d102d3fe5ad0062a92781acb16fa354a7b6c0d2f439", "unvendored_tests": false, "version": "1.11.0"}, "pyarrow": {"depends": ["numpy", "pandas", "pyodide-unix-timezones"], "file_name": "pyarrow-18.1.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["p<PERSON><PERSON>"], "install_dir": "site", "name": "p<PERSON><PERSON>", "package_type": "package", "sha256": "72c3d9bc1ba0dc807828548c03de885624341efc102846b75f6225bece3f02a6", "unvendored_tests": false, "version": "18.1.0"}, "pyclipper": {"depends": [], "file_name": "pyclipper-1.3.0.post5-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pyclipper"], "install_dir": "site", "name": "pyclipper", "package_type": "package", "sha256": "060d022426c6f8b05f1b36a446868057a63841cab190f010fbbb05d26a207045", "unvendored_tests": false, "version": "1.3.0.post5"}, "pycparser": {"depends": [], "file_name": "pycparser-2.22-py3-none-any.whl", "imports": ["pyc<PERSON><PERSON>"], "install_dir": "site", "name": "pyc<PERSON><PERSON>", "package_type": "package", "sha256": "9c907f3feea2a7cfdfcf6e2d95cf4de123ac41a9afc1a724fd72377385000385", "unvendored_tests": false, "version": "2.22"}, "pycryptodome": {"depends": [], "file_name": "pycryptodome-3.20.0-cp35-abi3-pyodide_2024_0_wasm32.whl", "imports": ["Crypto"], "install_dir": "site", "name": "pycryptodome", "package_type": "package", "sha256": "a8f3ee4c770ab953b83a1d6f21dbddea08c4b81b80bd5a6c90cd7d0500bbc3bc", "unvendored_tests": true, "version": "3.20.0"}, "pycryptodome-tests": {"depends": ["pycryptodome"], "file_name": "pycryptodome-tests.tar", "imports": [], "install_dir": "site", "name": "pycryptodome-tests", "package_type": "package", "sha256": "c3ff3a8128df4043b238d76ad9ef5177b6c93d0e20532361e5b097f5ccc1517f", "unvendored_tests": false, "version": "3.20.0"}, "pydantic": {"depends": ["typing-extensions", "pydantic_core", "annotated-types"], "file_name": "pydantic-2.10.5-py3-none-any.whl", "imports": ["pydantic"], "install_dir": "site", "name": "pydantic", "package_type": "package", "sha256": "dd344e353e3c3a2d972c1198744a4f4ba1e2ce586e903187cca9fdaafb46c63d", "unvendored_tests": false, "version": "2.10.5"}, "pydantic-core": {"depends": [], "file_name": "pydantic_core-2.27.2-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pydantic_core"], "install_dir": "site", "name": "pydantic_core", "package_type": "package", "sha256": "eefd735db79e36e3425e4a821fc9df6cb172d2845f0d4a2a1038eb5f4d5c1d0c", "unvendored_tests": false, "version": "2.27.2"}, "pydecimal": {"depends": [], "file_name": "pydecimal-1.0.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["_pydecimal"], "install_dir": "site", "name": "pydecimal", "package_type": "cpython_module", "sha256": "b580c39e5573e4a22be030aec5ef141fbb725f52a3ef71d0ee903c3d17c01d2b", "unvendored_tests": false, "version": "1.0.0"}, "pydoc-data": {"depends": [], "file_name": "pydoc_data-1.0.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pydoc_data"], "install_dir": "site", "name": "pydoc_data", "package_type": "cpython_module", "sha256": "7b0b5ff6ecc0dbfbc644516d9b87c4ae78c291c15334ae89aab9e1f24ca0ea9a", "unvendored_tests": false, "version": "1.0.0"}, "pyerfa": {"depends": ["numpy"], "file_name": "pyerfa-2.0.1.4-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["erfa"], "install_dir": "site", "name": "pyerfa", "package_type": "package", "sha256": "3188ea139850f6e7cd8424af15795d14608ec37a60e47bf7387e2ebdd0c34bce", "unvendored_tests": true, "version": "2.0.1.4"}, "pyerfa-tests": {"depends": ["pyerfa"], "file_name": "pyerfa-tests.tar", "imports": [], "install_dir": "site", "name": "pyerfa-tests", "package_type": "package", "sha256": "867d678d47748bda8acbfba72c9d3c2399d2a21cfae83b3dde9a22d109e0e59e", "unvendored_tests": false, "version": "2.0.1.4"}, "pygame-ce": {"depends": [], "file_name": "pygame_ce-2.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pygame"], "install_dir": "site", "name": "pygame-ce", "package_type": "package", "sha256": "4ce0d35621b6a67bd66db8d3d4f3531774a8cf80edc0faecf76e33762ff71ef1", "unvendored_tests": true, "version": "2.4.1"}, "pygame-ce-tests": {"depends": ["pygame-ce"], "file_name": "pygame-ce-tests.tar", "imports": [], "install_dir": "site", "name": "pygame-ce-tests", "package_type": "package", "sha256": "6cc0398701cea4986d11ffb75bc488cd6068443d6c1c77ee799de5e0300e5c71", "unvendored_tests": false, "version": "2.4.1"}, "pygments": {"depends": [], "file_name": "pygments-2.17.2-py3-none-any.whl", "imports": ["pygments"], "install_dir": "site", "name": "Pygments", "package_type": "package", "sha256": "f32060883fc37e539491d0f6626aa861f2f484cceb8ae3c1364b482d1dde0e38", "unvendored_tests": false, "version": "2.17.2"}, "pyheif": {"depends": ["cffi"], "file_name": "pyheif-0.8.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["py<PERSON><PERSON>"], "install_dir": "site", "name": "py<PERSON><PERSON>", "package_type": "package", "sha256": "5bc64d93fbdabd7c2344fda77d8c0280eba4995b417a7a645b30c7770a27aec3", "unvendored_tests": false, "version": "0.8.0"}, "pyiceberg": {"depends": ["click", "fsspec", "mmh3", "pydantic", "pyparsing", "requests", "rich", "sortedcontainers", "sqlalchemy", "strictyaml"], "file_name": "pyi<PERSON>berg-0.6.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "ed67c30c6e534ea38459e7469b08dfb8845c0f73ea45e61aed19c5a08f3c0ae9", "unvendored_tests": false, "version": "0.6.0"}, "pyinstrument": {"depends": [], "file_name": "pyinstrument-4.4.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pyinstrument"], "install_dir": "site", "name": "pyinstrument", "package_type": "package", "sha256": "625e11db231ad89fc896ad4835bdeb6aa3d1959cee9bf64cac7dccc3bf508de3", "unvendored_tests": false, "version": "4.4.0"}, "pynacl": {"depends": ["cffi"], "file_name": "pynacl-1.5.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["nacl"], "install_dir": "site", "name": "pynacl", "package_type": "package", "sha256": "b1404a2af8b8515f11e82b066a1c3dc5b976d990e2ac3c960b0a93cb628e77dd", "unvendored_tests": false, "version": "1.5.0"}, "pyodide-http": {"depends": [], "file_name": "pyodide_http-0.2.1-py3-none-any.whl", "imports": ["pyodide_http"], "install_dir": "site", "name": "pyodide-http", "package_type": "package", "sha256": "eba0df0bfddb0481625725cdf679af16cd6825ba686167a7b52719469a29267b", "unvendored_tests": false, "version": "0.2.1"}, "pyodide-unix-timezones": {"depends": [], "file_name": "pyodide_unix_timezones-1.0.0-py3-none-any.whl", "imports": ["unix_timezones"], "install_dir": "site", "name": "pyodide-unix-timezones", "package_type": "package", "sha256": "3f13dc510527a6496b777593b7e95c4ade6f0c0d7105647f495fe9520b77097c", "unvendored_tests": false, "version": "1.0.0"}, "pyparsing": {"depends": [], "file_name": "pyparsing-3.1.2-py3-none-any.whl", "imports": ["pyparsing"], "install_dir": "site", "name": "pyparsing", "package_type": "package", "sha256": "9070866557845375d8180e9236021fdecb2b93be75a7c8de48841f1465133a60", "unvendored_tests": false, "version": "3.1.2"}, "pyproj": {"depends": ["certifi", "sqlite3"], "file_name": "pyproj-3.6.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "16489b4aaa6107abe65c4161e3f7c74714bd980305a66f6e517154466192c0a8", "unvendored_tests": false, "version": "3.6.1"}, "pyrsistent": {"depends": [], "file_name": "pyrsistent-0.20.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["_pyrsistent_version", "pyrsistent"], "install_dir": "site", "name": "pyrsistent", "package_type": "package", "sha256": "9b779e5dbee6eb26e493f86b510a199981eb58b3d5777391927143161991eb12", "unvendored_tests": false, "version": "0.20.0"}, "pysam": {"depends": [], "file_name": "pysam-0.22.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pysam"], "install_dir": "site", "name": "pysam", "package_type": "package", "sha256": "0d23f76d51543a216e521161a820b2d245c3b32c34acc29ccae1ed6bb417ee13", "unvendored_tests": false, "version": "0.22.0"}, "pyshp": {"depends": [], "file_name": "pyshp-2.3.1-py2.py3-none-any.whl", "imports": ["shapefile"], "install_dir": "site", "name": "pyshp", "package_type": "package", "sha256": "dfe6e10713df7b685bc8fa0d8c177cf5846ba32d60b84ca35e08adcc53b52503", "unvendored_tests": false, "version": "2.3.1"}, "pytest": {"depends": ["atomicwrites", "attrs", "more-itertools", "pluggy", "py", "setuptools", "six", "iniconfig", "exceptiongroup"], "file_name": "pytest-8.1.1-py3-none-any.whl", "imports": ["_pytest", "pytest"], "install_dir": "site", "name": "pytest", "package_type": "package", "sha256": "6807d755d66c89ea85cbc415eec062f59dbea7ef0de2cc862fdf326042fa0a3f", "unvendored_tests": false, "version": "8.1.1"}, "pytest-asyncio": {"depends": ["pytest"], "file_name": "pytest_asyncio-0.23.7-py3-none-any.whl", "imports": ["pytest_asyncio"], "install_dir": "site", "name": "pytest-asyncio", "package_type": "package", "sha256": "9bb0e70a86393a6a39b6ab0d310ea2c2ccbdc79bbe0ef8264290410952b75ecf", "unvendored_tests": false, "version": "0.23.7"}, "pytest-benchmark": {"depends": [], "file_name": "pytest_benchmark-4.0.0-py3-none-any.whl", "imports": ["pytest_benchmark"], "install_dir": "site", "name": "pytest-benchmark", "package_type": "package", "sha256": "eccc2a3f5f26346be28e84ceeb0c07d2c0de3caa03f856a92cfcc238c39a9ec9", "unvendored_tests": false, "version": "4.0.0"}, "pytest-httpx": {"depends": ["httpx", "pytest", "httpcore"], "file_name": "pytest_httpx-0.30.0-py3-none-any.whl", "imports": ["pytest_httpx"], "install_dir": "site", "name": "pytest_httpx", "package_type": "package", "sha256": "e8bae7ecaf4773c914aeec10e7b4cbf9f6e2fef7835bcb6b960f36dc29348faa", "unvendored_tests": false, "version": "0.30.0"}, "python-dateutil": {"depends": ["six"], "file_name": "python_dateutil-2.9.0.post0-py2.py3-none-any.whl", "imports": ["dateutil"], "install_dir": "site", "name": "python-dateutil", "package_type": "package", "sha256": "02811ea3714f6697d639c07dc5ec63f5774dc7a688e4e1cbbe9beb2f33c3e6c9", "unvendored_tests": false, "version": "2.9.0.post0"}, "python-flint": {"depends": [], "file_name": "python_flint-0.6.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["flint"], "install_dir": "site", "name": "python-flint", "package_type": "package", "sha256": "639412db89782e84e4f323f0c4134f9367e0f110c81a4d3d948bdeedfe87a811", "unvendored_tests": false, "version": "0.6.0"}, "python-magic": {"depends": ["libmagic"], "file_name": "python_magic-0.4.27-py2.py3-none-any.whl", "imports": ["magic"], "install_dir": "site", "name": "python-magic", "package_type": "package", "sha256": "cea35f65052af6d8bf890af06cf2198fa3b2c9a4e2faa5cf2488d43ed7734e5f", "unvendored_tests": false, "version": "0.4.27"}, "python-sat": {"depends": ["six"], "file_name": "python_sat-1.8.dev17-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pysat"], "install_dir": "site", "name": "python-sat", "package_type": "package", "sha256": "32594022aef9c142209a28527351575f091c0d0e573bda3c296461e2699f40c0", "unvendored_tests": false, "version": "1.8.dev17"}, "python-solvespace": {"depends": [], "file_name": "python_solvespace-3.0.8-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["python_solvespace"], "install_dir": "site", "name": "python-solvespace", "package_type": "package", "sha256": "0e3eecbca538c0b9911d8a7e126fd8096feaaec50de242af56e194825698d62a", "unvendored_tests": false, "version": "3.0.8"}, "pytz": {"depends": [], "file_name": "pytz-2024.1-py2.py3-none-any.whl", "imports": ["pytz"], "install_dir": "site", "name": "pytz", "package_type": "package", "sha256": "fcfc168155da8d19057b17bee735fef71dc3a39f1dccd83514e16ecc6abaddc4", "unvendored_tests": false, "version": "2024.1"}, "pywavelets": {"depends": ["numpy"], "file_name": "pywavelets-1.8.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["pywt"], "install_dir": "site", "name": "pywavelets", "package_type": "package", "sha256": "722b7a4daa4c1d074cf15f0193bf378c8a894da69df493c186cddc899b0e0e6d", "unvendored_tests": true, "version": "1.8.0"}, "pywavelets-tests": {"depends": ["pywavelets"], "file_name": "pywavelets-tests.tar", "imports": [], "install_dir": "site", "name": "pywavelets-tests", "package_type": "package", "sha256": "8245ff850020e90aa0531b7d339c0b53d9c1cdeb04cb24abe2e3639fcda934a8", "unvendored_tests": false, "version": "1.8.0"}, "pyxel": {"depends": [], "file_name": "pyxel-1.9.10-cp37-abi3-pyodide_2024_0_wasm32.whl", "imports": ["pyxel"], "install_dir": "site", "name": "pyxel", "package_type": "package", "sha256": "f7f2400169ba683e6d80e479aa272a48e3d642d8355c49ed7be2b6085f787c16", "unvendored_tests": false, "version": "1.9.10"}, "pyxirr": {"depends": [], "file_name": "pyxirr-0.10.6-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["pyxirr"], "install_dir": "site", "name": "pyxirr", "package_type": "package", "sha256": "1c5653e42432a864f742209a8341ca8184991a104b1fbbb7c8233d9108ff425e", "unvendored_tests": false, "version": "0.10.6"}, "pyyaml": {"depends": [], "file_name": "pyyaml-6.0.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["_yaml", "yaml"], "install_dir": "site", "name": "pyyaml", "package_type": "package", "sha256": "444d034ede13ca0a2961a1215838fb7d4cf6fa98ba7fe5e2a09e5063180af2bf", "unvendored_tests": false, "version": "6.0.2"}, "rasterio": {"depends": ["numpy", "affine", "gdal", "attrs", "certifi", "click", "cligj"], "file_name": "rasterio-1.4.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["rasterio"], "install_dir": "site", "name": "rasterio", "package_type": "package", "sha256": "30252f7e5fce6f9813ea8490e407a2edf941ddcf8b18fff522b836d4f63edbc6", "unvendored_tests": false, "version": "1.4.2"}, "rateslib": {"depends": ["numpy", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "rateslib-1.6.0-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["rateslib"], "install_dir": "site", "name": "rateslib", "package_type": "package", "sha256": "2c8af9571e2d9710ea040f171f51bce49d5965274bd54715d15af3a1f6ba5899", "unvendored_tests": false, "version": "1.6.0"}, "rebound": {"depends": ["numpy"], "file_name": "rebound-4.4.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["rebound"], "install_dir": "site", "name": "rebound", "package_type": "package", "sha256": "6694ccb785b604245e4c09cc6528a292c577008b349bc43cdacb72b5a43e2cae", "unvendored_tests": false, "version": "4.4.3"}, "reboundx": {"depends": ["rebound", "numpy"], "file_name": "reboundx-4.3.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["reboundx"], "install_dir": "site", "name": "reboundx", "package_type": "package", "sha256": "1691a8aec70c8fbcd10a14ae60194ce1f6869fff14900001116ea56099161022", "unvendored_tests": false, "version": "4.3.0"}, "referencing": {"depends": ["attrs", "rpds-py"], "file_name": "referencing-0.34.0-py3-none-any.whl", "imports": ["referencing"], "install_dir": "site", "name": "referencing", "package_type": "package", "sha256": "21cba0cad6e1bda83f12d98d7546e3035078d74b2cd8069c7b68131921032726", "unvendored_tests": true, "version": "0.34.0"}, "referencing-tests": {"depends": ["referencing"], "file_name": "referencing-tests.tar", "imports": [], "install_dir": "site", "name": "referencing-tests", "package_type": "package", "sha256": "31b4eaa934a9b7e2591d5051991662f0abed42cc3776332e8871108c7189d1d4", "unvendored_tests": false, "version": "0.34.0"}, "regex": {"depends": [], "file_name": "regex-2024.9.11-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["regex"], "install_dir": "site", "name": "regex", "package_type": "package", "sha256": "96abcf0bbb787d26ba73c2057ae5dd548a650740071860c37582c11037c7bf5d", "unvendored_tests": true, "version": "2024.9.11"}, "regex-tests": {"depends": ["regex"], "file_name": "regex-tests.tar", "imports": [], "install_dir": "site", "name": "regex-tests", "package_type": "package", "sha256": "991e1d190a414427fb4deb9f5e6b95c59149d89d95dec48eef92882ca8b0ff3c", "unvendored_tests": false, "version": "2024.9.11"}, "requests": {"depends": ["charset-normalizer", "idna", "urllib3", "certifi"], "file_name": "requests-2.31.0-py3-none-any.whl", "imports": ["requests"], "install_dir": "site", "name": "requests", "package_type": "package", "sha256": "aa9e565a1522a7b700e0801c429f7139c5e5c56d22c1b20e2987351a5e0cc48b", "unvendored_tests": false, "version": "2.31.0"}, "retrying": {"depends": ["six"], "file_name": "retrying-1.3.4-py3-none-any.whl", "imports": ["retrying"], "install_dir": "site", "name": "retrying", "package_type": "package", "sha256": "e6e09bfbf01b44ff3e8ca3ed57a5dd4a565806b1e417aa4c15483e93f4e9b595", "unvendored_tests": false, "version": "1.3.4"}, "rich": {"depends": [], "file_name": "rich-13.7.1-py3-none-any.whl", "imports": ["rich"], "install_dir": "site", "name": "rich", "package_type": "package", "sha256": "a0b51f5b5b661fbd0a5bfd82b6d09498e62c2e85a3f00ed9878cd58b5b0bf38d", "unvendored_tests": false, "version": "13.7.1"}, "river": {"depends": ["numpy", "pandas", "scipy"], "file_name": "river-0.22.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["river"], "install_dir": "site", "name": "river", "package_type": "package", "sha256": "5195e88e8391cadb6c110a0728c44c53ede82ccb9be384278887db69dec19dba", "unvendored_tests": true, "version": "0.22.0"}, "river-tests": {"depends": ["river"], "file_name": "river-tests.tar", "imports": [], "install_dir": "site", "name": "river-tests", "package_type": "package", "sha256": "46d2fd6e7191b088abd87dd0289ab3cc2162c3dff762dcce583c9e9b977abaeb", "unvendored_tests": false, "version": "0.22.0"}, "robotraconteur": {"depends": ["numpy"], "file_name": "robotraconteur-1.2.2-cp312-cp312-pyo<PERSON><PERSON>_2024_0_wasm32.whl", "imports": ["RobotRaconteur"], "install_dir": "site", "name": "RobotRaconteur", "package_type": "package", "sha256": "85ebc3675e9b9307976be92deb7a3742f09cb4b2d82a2f31c9371fe56f3ba248", "unvendored_tests": false, "version": "1.2.2"}, "rpds-py": {"depends": [], "file_name": "rpds_py-0.18.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["rpds"], "install_dir": "site", "name": "rpds-py", "package_type": "package", "sha256": "4236ab9baaadceecafa95941c0ee78b2fb96e97c1e9e6328a60a7a8a44dd9625", "unvendored_tests": false, "version": "0.18.0"}, "ruamel-yaml": {"depends": [], "file_name": "ruamel.yaml-0.18.6-py3-none-any.whl", "imports": ["rua<PERSON>"], "install_dir": "site", "name": "ruamel.yaml", "package_type": "package", "sha256": "164c5ff648a06276340cd108b6f3bbe5be73933d579cf424ab4d195cfd7a0c75", "unvendored_tests": false, "version": "0.18.6"}, "rust-abi-test": {"depends": [], "file_name": "rust_abi_test-1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["rust-abi-test"], "install_dir": "site", "name": "rust-abi-test", "package_type": "package", "sha256": "b4973404a367023dcfac6312c3a0cc375d9b97d2d8e57ddac852e68a770690c6", "unvendored_tests": false, "version": "1.0"}, "rust-panic-test": {"depends": [], "file_name": "rust_panic_test-1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["rust-panic-test"], "install_dir": "site", "name": "rust-panic-test", "package_type": "package", "sha256": "669e81d527fe0d10c371d4c223bc32895b45f6dc1b04adc566d9be0fae16175b", "unvendored_tests": false, "version": "1.0"}, "scikit-image": {"depends": ["packaging", "numpy", "scipy", "networkx", "pillow", "imageio", "pywavelets", "lazy_loader"], "file_name": "scikit_image-0.25.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["skimage"], "install_dir": "site", "name": "scikit-image", "package_type": "package", "sha256": "788beea853f8cf25a3d4847e5fe7de9c80045491e12e49842f1c3b16fbcd6db8", "unvendored_tests": true, "version": "0.25.0"}, "scikit-image-tests": {"depends": ["scikit-image"], "file_name": "scikit-image-tests.tar", "imports": [], "install_dir": "site", "name": "scikit-image-tests", "package_type": "package", "sha256": "801056f0607db2f8b279d90b2df2bf22cf3f05e9e1a785a9be4ee83edf48c98d", "unvendored_tests": false, "version": "0.25.0"}, "scikit-learn": {"depends": ["scipy", "joblib", "threadpoolctl"], "file_name": "scikit_learn-1.6.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["sklearn"], "install_dir": "site", "name": "scikit-learn", "package_type": "package", "sha256": "022fe0061765894279b4ae1035e40da2cb436efa51d408f8e6214aacad0c631b", "unvendored_tests": true, "version": "1.6.1"}, "scikit-learn-tests": {"depends": ["scikit-learn"], "file_name": "scikit-learn-tests.tar", "imports": [], "install_dir": "site", "name": "scikit-learn-tests", "package_type": "package", "sha256": "f05a45caba47a998c0bf7a7adf8ca68e781e784830aa641c65b2794aa5115af1", "unvendored_tests": false, "version": "1.6.1"}, "scipy": {"depends": ["numpy", "openblas"], "file_name": "scipy-1.14.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["scipy"], "install_dir": "site", "name": "scipy", "package_type": "package", "sha256": "997c50a61564a8ce6a56ea98a42e7f4427e5eb98475a73233e83df6a184f415c", "unvendored_tests": true, "version": "1.14.1"}, "scipy-tests": {"depends": ["scipy"], "file_name": "scipy-tests.tar", "imports": [], "install_dir": "site", "name": "scipy-tests", "package_type": "package", "sha256": "cebd7149650a3c52b0fdb466bebf1a613085857785d89574f6d189dc1a78f969", "unvendored_tests": false, "version": "1.14.1"}, "screed": {"depends": [], "file_name": "screed-1.1.3-py2.py3-none-any.whl", "imports": ["bigtests", "screed"], "install_dir": "site", "name": "screed", "package_type": "package", "sha256": "5bcd0e4a52df47e418304762085fd7e5ca514292089baedd420d871b7df01814", "unvendored_tests": true, "version": "1.1.3"}, "screed-tests": {"depends": ["screed"], "file_name": "screed-tests.tar", "imports": [], "install_dir": "site", "name": "screed-tests", "package_type": "package", "sha256": "bfa4d4bb7c62e90093868bcc7546a8b42b7edac56a9c57b03473b871737285dc", "unvendored_tests": false, "version": "1.1.3"}, "setuptools": {"depends": ["pyparsing"], "file_name": "setuptools-69.5.1-py3-none-any.whl", "imports": ["_distutils_hack", "pkg_resources", "setuptools"], "install_dir": "site", "name": "setuptools", "package_type": "package", "sha256": "6a44af4c5bbd6ae019fbe5fe52e6f3bff3b02ee6cbc543ebdf89d113b1c88c87", "unvendored_tests": false, "version": "69.5.1"}, "shapely": {"depends": ["numpy"], "file_name": "shapely-2.0.6-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["shapely"], "install_dir": "site", "name": "shapely", "package_type": "package", "sha256": "5a1354ccee47c150f5fc764e2f1ec7cc6aa2a4e9f028c0e9de5b83c5b5d53026", "unvendored_tests": true, "version": "2.0.6"}, "shapely-tests": {"depends": ["shapely"], "file_name": "shapely-tests.tar", "imports": [], "install_dir": "site", "name": "shapely-tests", "package_type": "package", "sha256": "c21cef6e8c2bacf606b06a47fb2f24f685fdd456e4d7581071cd72b9c89ce14c", "unvendored_tests": false, "version": "2.0.6"}, "sharedlib-test": {"depends": [], "file_name": "sharedlib-test-1.0.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "sharedlib-test", "package_type": "shared_library", "sha256": "8a139430acb582c33b14822f8f40c7f827f4d39b685696d7f52819fc6a72e2a8", "unvendored_tests": false, "version": "1.0"}, "sharedlib-test-py": {"depends": ["sharedlib-test"], "file_name": "sharedlib_test_py-1.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["sharedlib_test"], "install_dir": "site", "name": "sharedlib-test-py", "package_type": "package", "sha256": "34d2ba1e2da9ad2f605f130e9a22ad686f2ebcaa26a3681f38f00e4d3b20b467", "unvendored_tests": false, "version": "1.0"}, "simplejson": {"depends": [], "file_name": "simplejson-3.19.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["<PERSON><PERSON><PERSON>"], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>", "package_type": "package", "sha256": "924eff7af1fa0496c6edf381093336906ed510eccddbdfa87de5457a9a1be6da", "unvendored_tests": true, "version": "3.19.2"}, "simplejson-tests": {"depends": ["<PERSON><PERSON><PERSON>"], "file_name": "simplejson-tests.tar", "imports": [], "install_dir": "site", "name": "<PERSON><PERSON><PERSON>-tests", "package_type": "package", "sha256": "72c03bc5810643a43371cffe523ef7996a583b8e373580692077a2ccc991abba", "unvendored_tests": false, "version": "3.19.2"}, "sisl": {"depends": ["pyparsing", "numpy", "scipy", "tqdm", "xarray", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "sisl-0.15.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["sisl_toolbox", "sisl"], "install_dir": "site", "name": "sisl", "package_type": "package", "sha256": "360da9984c4a45a7e722e4a89701455e6aa9e5eb1ae5e39b78acfb89ad03be04", "unvendored_tests": true, "version": "0.15.1"}, "sisl-tests": {"depends": ["sisl"], "file_name": "sisl-tests.tar", "imports": [], "install_dir": "site", "name": "sisl-tests", "package_type": "package", "sha256": "dcfb63e5681592a006d9cd0c48ac7dfb0c239759c9285fcf12238b9c782f8298", "unvendored_tests": false, "version": "0.15.1"}, "six": {"depends": [], "file_name": "six-1.16.0-py2.py3-none-any.whl", "imports": ["six"], "install_dir": "site", "name": "six", "package_type": "package", "sha256": "f61235bc3a15086f0369585e5071ae9ba0bd244a111d12b37f683862e6850c0a", "unvendored_tests": false, "version": "1.16.0"}, "smart-open": {"depends": ["wrapt"], "file_name": "smart_open-7.0.4-py3-none-any.whl", "imports": ["smart_open"], "install_dir": "site", "name": "smart-open", "package_type": "package", "sha256": "2c4c355114e82c921aed53926553bfd18dea47ba13bb9b42d22d35a3bd212d04", "unvendored_tests": false, "version": "7.0.4"}, "sniffio": {"depends": [], "file_name": "sniffio-1.3.1-py3-none-any.whl", "imports": ["sniffio"], "install_dir": "site", "name": "sniffio", "package_type": "package", "sha256": "9215f9917b34fc73152b134a3fc0a2eb0e4a49b0b956100cad75e84943412bb9", "unvendored_tests": true, "version": "1.3.1"}, "sniffio-tests": {"depends": ["sniffio"], "file_name": "sniffio-tests.tar", "imports": [], "install_dir": "site", "name": "sniffio-tests", "package_type": "package", "sha256": "4359e1a6b2fd3ab2fba68a5c7fe97601cdb11b937922d9a56e5f9c41e32f244e", "unvendored_tests": false, "version": "1.3.1"}, "sortedcontainers": {"depends": [], "file_name": "sortedcontainers-2.4.0-py2.py3-none-any.whl", "imports": ["sortedcontainers"], "install_dir": "site", "name": "sortedcontainers", "package_type": "package", "sha256": "921a3fdbab3f20015bf39eb0f06807891b78080f39baf92376d1e73038562d27", "unvendored_tests": false, "version": "2.4.0"}, "soupsieve": {"depends": [], "file_name": "soupsieve-2.5-py3-none-any.whl", "imports": ["soupsieve"], "install_dir": "site", "name": "soupsieve", "package_type": "package", "sha256": "e18a6ff6869082a37e78db6e688b239e8f638d647a2e8e3e152f7dee81c262eb", "unvendored_tests": false, "version": "2.5"}, "sourmash": {"depends": ["screed", "cffi", "deprecation", "cachetools", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scipy", "sqlite3", "bitstring"], "file_name": "sourmash-4.8.11-py3-none-pyodide_2024_0_wasm32.whl", "imports": ["sourmash"], "install_dir": "site", "name": "sourmash", "package_type": "package", "sha256": "ae9c653de12b5cf4b215e4e6fe4ee5d91ce80e10335805265a8681c019c42ff1", "unvendored_tests": false, "version": "4.8.11"}, "soxr": {"depends": ["numpy"], "file_name": "soxr-0.5.0.post1-cp312-abi3-pyodide_2024_0_wasm32.whl", "imports": ["soxr"], "install_dir": "site", "name": "soxr", "package_type": "package", "sha256": "90f77f06c07dbc1fc09a1901f9a94565f8ae632b4827cf4b5f3ba935ab8c9def", "unvendored_tests": false, "version": "0.5.0.post1"}, "sparseqr": {"depends": ["pyc<PERSON><PERSON>", "cffi", "numpy", "scipy", "suitesparse"], "file_name": "sparseqr-1.2-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["sparseqr"], "install_dir": "site", "name": "sparseqr", "package_type": "package", "sha256": "d0f8222b9e1fda78c125ca526d89649ade8b1da1e8cebd7e031a762284694960", "unvendored_tests": false, "version": "1.2"}, "sqlalchemy": {"depends": ["sqlite3", "typing-extensions"], "file_name": "sqlalchemy-2.0.29-cp312-cp312-pyo<PERSON><PERSON>_2024_0_wasm32.whl", "imports": ["sqlalchemy"], "install_dir": "site", "name": "sqlalchemy", "package_type": "package", "sha256": "c6ea5157694bf17915f33311d5064d36c5e73f9b872375571a8927d66c96ef28", "unvendored_tests": true, "version": "2.0.29"}, "sqlalchemy-tests": {"depends": ["sqlalchemy"], "file_name": "sqlalchemy-tests.tar", "imports": [], "install_dir": "site", "name": "sqlalchemy-tests", "package_type": "package", "sha256": "4ebd5b0307df4bc774984d5c782aacffea13dafeb7ce26409b925bb2174290c4", "unvendored_tests": false, "version": "2.0.29"}, "sqlite3": {"depends": [], "file_name": "sqlite3-1.0.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["sqlite3", "_sqlite3"], "install_dir": "site", "name": "sqlite3", "package_type": "cpython_module", "sha256": "43061928762b03482676a1e11dcef99a003148e59fe914b6a3e41180c382cfc3", "unvendored_tests": false, "version": "1.0.0"}, "ssl": {"depends": ["openssl"], "file_name": "ssl-1.0.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["ssl", "_ssl"], "install_dir": "site", "name": "ssl", "package_type": "cpython_module", "sha256": "1af0b011a3043331a8a2a358832dcee736deff73a55ad0393222ea533942094a", "unvendored_tests": false, "version": "1.0.0"}, "stack-data": {"depends": ["executing", "asttokens", "pure-eval"], "file_name": "stack_data-0.6.3-py3-none-any.whl", "imports": ["stack_data"], "install_dir": "site", "name": "stack-data", "package_type": "package", "sha256": "650be7bb2e224e51b3f872abc6c503323b0e12c2090121c43cb576b06da7c8c6", "unvendored_tests": false, "version": "0.6.3"}, "statsmodels": {"depends": ["numpy", "scipy", "pandas", "patsy", "packaging"], "file_name": "statsmodels-0.14.4-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["statsmodels"], "install_dir": "site", "name": "statsmodels", "package_type": "package", "sha256": "4c89f4c9146b142216126042af0eb3927d5912485a50bfd333def06c5ad5fd8d", "unvendored_tests": false, "version": "0.14.4"}, "strictyaml": {"depends": ["python-dateutil"], "file_name": "strictyaml-1.7.3-py3-none-any.whl", "imports": ["strictyaml"], "install_dir": "site", "name": "strictyaml", "package_type": "package", "sha256": "9984cb99d952f8f4058af9644ba8c256a66540e280a28a228340d195df6a2b2d", "unvendored_tests": false, "version": "1.7.3"}, "suitesparse": {"depends": ["openblas"], "file_name": "suitesparse-5.11.0.zip", "imports": [], "install_dir": "dyn<PERSON>b", "name": "suitesparse", "package_type": "shared_library", "sha256": "5119c1c844aa457387eb24656c1f5804de6535509d944e9693f9b577ba352b29", "unvendored_tests": false, "version": "5.11.0"}, "svgwrite": {"depends": [], "file_name": "svgwrite-1.4.3-py3-none-any.whl", "imports": ["svgwrite"], "install_dir": "site", "name": "svgwrite", "package_type": "package", "sha256": "4cb6d950e100b7c1a0ee854d3dd0b1e212a9740529b2535e9bcd1cfd84158f35", "unvendored_tests": false, "version": "1.4.3"}, "swiglpk": {"depends": [], "file_name": "swiglpk-5.0.10-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["swiglpk"], "install_dir": "site", "name": "swiglpk", "package_type": "package", "sha256": "c90b0ecc18b1e13302aa4ed8444093fe76eab584b0057fc57503dab800afde7a", "unvendored_tests": false, "version": "5.0.10"}, "sympy": {"depends": ["mpmath"], "file_name": "sympy-1.13.3-py3-none-any.whl", "imports": ["isympy", "sympy"], "install_dir": "site", "name": "sympy", "package_type": "package", "sha256": "f36c07ec53e3992f8d02ea833e4e277c51138791f07b508677dd37ef5c2965dd", "unvendored_tests": true, "version": "1.13.3"}, "sympy-tests": {"depends": ["sympy"], "file_name": "sympy-tests.tar", "imports": [], "install_dir": "site", "name": "sympy-tests", "package_type": "package", "sha256": "aab18f26d950dc211c82b1d070282a06e4f05bb89b8ed6298dfd5f431a7f020c", "unvendored_tests": false, "version": "1.13.3"}, "tblib": {"depends": [], "file_name": "tblib-3.0.0-py3-none-any.whl", "imports": ["tblib"], "install_dir": "site", "name": "tblib", "package_type": "package", "sha256": "ed436e5f8ae0a4199274895bc80d8df84c4fe0f0ed788b731944a12f8608bdba", "unvendored_tests": false, "version": "3.0.0"}, "termcolor": {"depends": [], "file_name": "termcolor-2.4.0-py3-none-any.whl", "imports": ["termcolor"], "install_dir": "site", "name": "termcolor", "package_type": "package", "sha256": "80206bea9d03e428ffa5bcde984cfad8e8460e0a8063aca066ce0a032e438f58", "unvendored_tests": false, "version": "2.4.0"}, "test": {"depends": [], "file_name": "test-1.0.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["test"], "install_dir": "site", "name": "test", "package_type": "cpython_module", "sha256": "f8ebd5e59c74ec825595874f45b88d061f486bee48b6e721bda5cf3489dc24e3", "unvendored_tests": false, "version": "1.0.0"}, "texttable": {"depends": [], "file_name": "texttable-1.7.0-py2.py3-none-any.whl", "imports": ["texttable"], "install_dir": "site", "name": "texttable", "package_type": "package", "sha256": "6f8af2339378fc488d2510b6b5096b34b57df7124150d6f4efee8c51f2c86dfb", "unvendored_tests": false, "version": "1.7.0"}, "threadpoolctl": {"depends": [], "file_name": "threadpoolctl-3.5.0-py3-none-any.whl", "imports": ["threadpoolctl"], "install_dir": "site", "name": "threadpoolctl", "package_type": "package", "sha256": "c463e5400524d9edd309fc100f362d774207d1e51321b92b258403ee6c3ba49c", "unvendored_tests": false, "version": "3.5.0"}, "tiktoken": {"depends": ["regex", "requests"], "file_name": "tiktoken-0.8.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["tiktoken", "tiktoken_ext"], "install_dir": "site", "name": "tiktoken", "package_type": "package", "sha256": "a8d15e3774b994267a998cc759b82ad6c0013f8200b58c7cd6b69607e1a831c3", "unvendored_tests": false, "version": "0.8.0"}, "tomli": {"depends": [], "file_name": "tomli-2.0.1-py3-none-any.whl", "imports": ["to<PERSON>li"], "install_dir": "site", "name": "to<PERSON>li", "package_type": "package", "sha256": "b4a588f7abeb4004dae12fd11792b6d7e7ebaee4cd7a17803764d9223640139d", "unvendored_tests": false, "version": "2.0.1"}, "tomli-w": {"depends": [], "file_name": "tomli_w-1.0.0-py3-none-any.whl", "imports": ["tomli_w"], "install_dir": "site", "name": "tomli-w", "package_type": "package", "sha256": "d2167ae892ab64baf827c89479414c42df7b97156f2b7a3c8af09ef13b27486b", "unvendored_tests": false, "version": "1.0.0"}, "toolz": {"depends": [], "file_name": "toolz-0.12.1-py3-none-any.whl", "imports": ["tlz", "toolz"], "install_dir": "site", "name": "toolz", "package_type": "package", "sha256": "8011c87ab3527478368d34365a5a402eacbd2d525e799f8d99cbf7f3ea2b2a16", "unvendored_tests": true, "version": "0.12.1"}, "toolz-tests": {"depends": ["toolz"], "file_name": "toolz-tests.tar", "imports": [], "install_dir": "site", "name": "toolz-tests", "package_type": "package", "sha256": "b904e1786e5cb8e5c8a45a0a11527350a217ac96d9af2be95b7182a4a5c9e9b6", "unvendored_tests": false, "version": "0.12.1"}, "tqdm": {"depends": [], "file_name": "tqdm-4.66.2-py3-none-any.whl", "imports": ["tqdm"], "install_dir": "site", "name": "tqdm", "package_type": "package", "sha256": "bc00a49b27c4e119f1f79ed363b01d6a7d7c50c6360fb2a4e0760568a0642e6d", "unvendored_tests": false, "version": "4.66.2"}, "traitlets": {"depends": [], "file_name": "traitlets-5.14.3-py3-none-any.whl", "imports": ["traitlets"], "install_dir": "site", "name": "traitlets", "package_type": "package", "sha256": "10548e49e1e17ac87a22080ab40bb818e1123302efa60893ea4ac335a24f86ee", "unvendored_tests": true, "version": "5.14.3"}, "traitlets-tests": {"depends": ["traitlets"], "file_name": "traitlets-tests.tar", "imports": [], "install_dir": "site", "name": "traitlets-tests", "package_type": "package", "sha256": "97c851c135696620e2c46574bcb0070a3a5ae2a505b9e14afe3c2b21339c7f20", "unvendored_tests": false, "version": "5.14.3"}, "traits": {"depends": [], "file_name": "traits-6.4.3-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["traits"], "install_dir": "site", "name": "traits", "package_type": "package", "sha256": "1cfd691e300a8523ce304a8005dd08834feab48ae49a1e79d34d64d54a74a853", "unvendored_tests": true, "version": "6.4.3"}, "traits-tests": {"depends": ["traits"], "file_name": "traits-tests.tar", "imports": [], "install_dir": "site", "name": "traits-tests", "package_type": "package", "sha256": "81fdf68ee2a9588f211b6471d2c9491e03433d0aedf00b7f77e876a340e5687a", "unvendored_tests": false, "version": "6.4.3"}, "tree-sitter": {"depends": [], "file_name": "tree_sitter-0.23.2-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["tree_sitter"], "install_dir": "site", "name": "tree-sitter", "package_type": "package", "sha256": "9a68166943480f8691cbc55d3e0749c8cc17554c65ec01326285ebb2cee1f696", "unvendored_tests": false, "version": "0.23.2"}, "tree-sitter-go": {"depends": ["tree-sitter"], "file_name": "tree_sitter_go-0.23.3-cp39-abi3-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["tree_sitter_go"], "install_dir": "site", "name": "tree-sitter-go", "package_type": "package", "sha256": "c42451ae2de7fbca6d2f4be2a9eff7d30582a2bb8eab3a9642f4025962669e12", "unvendored_tests": false, "version": "0.23.3"}, "tree-sitter-java": {"depends": ["tree-sitter"], "file_name": "tree_sitter_java-0.23.4-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["tree_sitter_java"], "install_dir": "site", "name": "tree-sitter-java", "package_type": "package", "sha256": "a6511fe471f7a022e92ac009ed97d3423226ab7522a7473f93a03f2b3483835d", "unvendored_tests": false, "version": "0.23.4"}, "tree-sitter-python": {"depends": ["tree-sitter"], "file_name": "tree_sitter_python-0.23.4-cp39-abi3-pyodide_2024_0_wasm32.whl", "imports": ["tree_sitter_python"], "install_dir": "site", "name": "tree-sitter-python", "package_type": "package", "sha256": "d948a8f07a3066db2c15b050fae0b5f15283c4466e83ae9017e21d669be3a620", "unvendored_tests": false, "version": "0.23.4"}, "tskit": {"depends": ["numpy", "svgwrite", "jsonschema", "rpds-py"], "file_name": "tskit-0.6.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["tskit"], "install_dir": "site", "name": "tskit", "package_type": "package", "sha256": "fd0fc875732b360ea00463835991d47d708a78b5f6f41b82c55a164c512f3f5c", "unvendored_tests": false, "version": "0.6.0"}, "typing-extensions": {"depends": [], "file_name": "typing_extensions-4.11.0-py3-none-any.whl", "imports": ["typing_extensions"], "install_dir": "site", "name": "typing-extensions", "package_type": "package", "sha256": "c3f00b6653202513b1f7813ad3c466510ceecc599a37d665a31dcfb5ac7049b9", "unvendored_tests": false, "version": "4.11.0"}, "tzdata": {"depends": [], "file_name": "tzdata-2024.1-py2.py3-none-any.whl", "imports": ["tzdata"], "install_dir": "site", "name": "tzdata", "package_type": "package", "sha256": "3f0d487be1b8180e8bfd72709a7727734cd1e02180d6a75e70d4232765215848", "unvendored_tests": false, "version": "2024.1"}, "uncertainties": {"depends": ["future"], "file_name": "uncertainties-3.1.7-py2.py3-none-any.whl", "imports": ["uncertainties"], "install_dir": "site", "name": "uncertainties", "package_type": "package", "sha256": "ca7bb1386e42f33fb78beba09af50eb1bf62c8e8fb2cd18180a42e116a5d4fa2", "unvendored_tests": true, "version": "3.1.7"}, "uncertainties-tests": {"depends": ["uncertainties"], "file_name": "uncertainties-tests.tar", "imports": [], "install_dir": "site", "name": "uncertainties-tests", "package_type": "package", "sha256": "af60023b960585dc4b58abc5680f664d2d71fd2461a2fa3f05884faf3a710b9b", "unvendored_tests": false, "version": "3.1.7"}, "unyt": {"depends": ["numpy", "packaging", "sympy"], "file_name": "unyt-3.0.3-py3-none-any.whl", "imports": ["unyt"], "install_dir": "site", "name": "unyt", "package_type": "package", "sha256": "2c2b2f7eacae8005c2cd4b55466653dd1fac1965683ef57b5a2ffaa7238a1f76", "unvendored_tests": true, "version": "3.0.3"}, "unyt-tests": {"depends": ["unyt"], "file_name": "unyt-tests.tar", "imports": [], "install_dir": "site", "name": "unyt-tests", "package_type": "package", "sha256": "42b1ecb29fc8bbbcacbbd72f87ddb301b72adc4bbe4a7a1c7b4ff2a0bf253d19", "unvendored_tests": false, "version": "3.0.3"}, "urllib3": {"depends": [], "file_name": "urllib3-2.2.3-py3-none-any.whl", "imports": ["urllib3"], "install_dir": "site", "name": "urllib3", "package_type": "package", "sha256": "902ea205c74f46402ea539ac68a7095117d20734c7ee6b7a6de1ca88224604f9", "unvendored_tests": false, "version": "2.2.3"}, "vega-datasets": {"depends": ["pandas"], "file_name": "vega_datasets-0.9.0-py3-none-any.whl", "imports": ["vega_datasets"], "install_dir": "site", "name": "vega-datasets", "package_type": "package", "sha256": "787417007f135977903ef362feb6b316a8e8ed404095ee04f47ffd5db6ba4512", "unvendored_tests": true, "version": "0.9.0"}, "vega-datasets-tests": {"depends": ["vega-datasets"], "file_name": "vega-datasets-tests.tar", "imports": [], "install_dir": "site", "name": "vega-datasets-tests", "package_type": "package", "sha256": "8e75ecd75a50546c5f5d8a1d36fb593a70913e54c2e4e753c3bf30c0d9c90c26", "unvendored_tests": false, "version": "0.9.0"}, "wcwidth": {"depends": [], "file_name": "wcwidth-0.2.13-py2.py3-none-any.whl", "imports": ["wcwidth"], "install_dir": "site", "name": "wcwidth", "package_type": "package", "sha256": "ae8fe34f6b43cadbd0db1a5a02a147cd4570915ae603032322f0a160aa12aaba", "unvendored_tests": false, "version": "0.2.13"}, "webencodings": {"depends": [], "file_name": "webencodings-0.5.1-py2.py3-none-any.whl", "imports": ["webencodings"], "install_dir": "site", "name": "webencodings", "package_type": "package", "sha256": "87f31ffae209d3e24545feb75d17e18c1ac87bb8304cd21a73eca2939d05f08d", "unvendored_tests": false, "version": "0.5.1"}, "wordcloud": {"depends": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "file_name": "wordcloud-1.9.3-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["wordcloud"], "install_dir": "site", "name": "wordcloud", "package_type": "package", "sha256": "f433b5946e3b963feb38818d97335b1f5ad4faad49a209c2c8656fbe374605a6", "unvendored_tests": false, "version": "1.9.3"}, "wrapt": {"depends": [], "file_name": "wrapt-1.16.0-cp312-cp312-pyodide_2024_0_wasm32.whl", "imports": ["wrapt"], "install_dir": "site", "name": "wrapt", "package_type": "package", "sha256": "4a1dd560aba11b9066f2558996df2c817fdc3894eebb5b031291ca3ced6b6ed5", "unvendored_tests": false, "version": "1.16.0"}, "xarray": {"depends": ["numpy", "packaging", "pandas"], "file_name": "xarray-2024.11.0-py3-none-any.whl", "imports": ["xarray"], "install_dir": "site", "name": "xarray", "package_type": "package", "sha256": "b01d67f12aaa1845655c25fcfd079268b4850f13681e030b7a732cdb7253f2a7", "unvendored_tests": true, "version": "2024.11.0"}, "xarray-tests": {"depends": ["xarray"], "file_name": "xarray-tests.tar", "imports": [], "install_dir": "site", "name": "xarray-tests", "package_type": "package", "sha256": "9d3c2fc81f033b74ca0303a4fc76c560e207aa0b2d6c205749660177147987aa", "unvendored_tests": false, "version": "2024.11.0"}, "xgboost": {"depends": ["numpy", "scipy", "setuptools"], "file_name": "xgboost-2.1.2-py3-none-pyodide_2024_0_wasm32.whl", "imports": ["xgboost"], "install_dir": "site", "name": "xgboost", "package_type": "package", "sha256": "0a283669c0720adb74fe4ccbeeb36bcefc6452adaa266d5c32293e427085f5d4", "unvendored_tests": false, "version": "2.1.2"}, "xlrd": {"depends": [], "file_name": "xlrd-2.0.1-py2.py3-none-any.whl", "imports": ["xlrd"], "install_dir": "site", "name": "xlrd", "package_type": "package", "sha256": "773ba42b16dffbc7475a0922574b61703fc1a59508ea4fefbb61e3e5aca3d7c3", "unvendored_tests": false, "version": "2.0.1"}, "xxhash": {"depends": [], "file_name": "xxhash-3.4.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["xxhash"], "install_dir": "site", "name": "xxhash", "package_type": "package", "sha256": "256f1841d90a000f0b2bee76850a9d68975aed446c4449777710a7398591dfd8", "unvendored_tests": false, "version": "3.4.1"}, "xyzservices": {"depends": [], "file_name": "xyzservices-2024.4.0-py3-none-any.whl", "imports": ["xyzservices"], "install_dir": "site", "name": "xyzservices", "package_type": "package", "sha256": "b157f591ed083cf9caa8554b348f3654133042a9ead9585176cb18b3eb2149d9", "unvendored_tests": true, "version": "2024.4.0"}, "xyzservices-tests": {"depends": ["xyzservices"], "file_name": "xyzservices-tests.tar", "imports": [], "install_dir": "site", "name": "xyzservices-tests", "package_type": "package", "sha256": "08a9053a13a8c68abe90e86cd49d8ad1b5cf8367b2e7badf8271b7f1ee01d4e2", "unvendored_tests": false, "version": "2024.4.0"}, "yarl": {"depends": ["multidict", "idna"], "file_name": "yarl-1.9.4-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["yarl"], "install_dir": "site", "name": "yarl", "package_type": "package", "sha256": "3d66511b55c1f05d65214b602237<PERSON><PERSON><PERSON><PERSON><PERSON>64c658ceb4a438c2af172b5729c", "unvendored_tests": false, "version": "1.9.4"}, "yt": {"depends": ["ewah_bool_utils", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sympy", "setuptools", "packaging", "unyt", "cmyt", "colorspacious", "tqdm", "to<PERSON>li", "tomli-w"], "file_name": "yt-4.3.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["yt"], "install_dir": "site", "name": "yt", "package_type": "package", "sha256": "ed5a165851b68a7fb585ff57ecdda0e94f5d34ce6121602640ce96fe61eeccf9", "unvendored_tests": false, "version": "4.3.1"}, "zarr": {"depends": ["numpy", "as<PERSON><PERSON><PERSON>", "numcodecs"], "file_name": "zarr-2.18.3-py3-none-any.whl", "imports": ["zarr"], "install_dir": "site", "name": "zarr", "package_type": "package", "sha256": "e05fcda26363d310d40440520e79ef84004d9d25227a1fd4a46b81ab89eeba3a", "unvendored_tests": true, "version": "2.18.3"}, "zarr-tests": {"depends": ["zarr"], "file_name": "zarr-tests.tar", "imports": [], "install_dir": "site", "name": "zarr-tests", "package_type": "package", "sha256": "48a7c9e3be63b05c513e3f6d349b432d2179e3bd6a6696845c1a9fb9c258d236", "unvendored_tests": false, "version": "2.18.3"}, "zengl": {"depends": [], "file_name": "zengl-2.7.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["zengl", "_zengl"], "install_dir": "site", "name": "zengl", "package_type": "package", "sha256": "1792f79ca05ebf3afb10b1a06881dd6680bfe52d7c9896e34899fa3e8435cbeb", "unvendored_tests": false, "version": "2.7.1"}, "zfpy": {"depends": ["numpy"], "file_name": "zfpy-1.0.1-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["zfpy"], "install_dir": "site", "name": "zfpy", "package_type": "package", "sha256": "beab06fc05ed3cdb8cb0fe403369d3f838fec88ebf5a4981e253fa74265ce072", "unvendored_tests": false, "version": "1.0.1"}, "zstandard": {"depends": ["cffi"], "file_name": "zstandard-0.22.0-cp312-cp312-pyodi<PERSON>_2024_0_wasm32.whl", "imports": ["zstandard"], "install_dir": "site", "name": "zstandard", "package_type": "package", "sha256": "8c83038208fd422dd2f1adaed69afabf7b3d6e72c7669fbb07a98dc3c8b8a049", "unvendored_tests": false, "version": "0.22.0"}}}