
> deer-flow-web@0.1.0 dev
> next dev --turbo

 ⚠ Warning: Found multiple lockfiles. Selecting /Users/<USER>/package-lock.json.
   Consider removing the lockfiles at:
   * /Users/<USER>/Documents/libra/mina-web/web/pnpm-lock.yaml
   * /Users/<USER>/Documents/libra/mina-web/pnpm-lock.yaml

 ⚠ Port 3000 is in use by process 715
97944, using available port 3002 instead.
   ▲ Next.js 15.4.4 (Turbopack)
   - Local:        http://localhost:3002
   - Network:      http://*************:3002
   - Environments: .env.local
   - Experiments (use with caution):
     · optimizePackageImports

 ✓ Starting...
 ✓ Ready in 2.2s
 ⚠ Webpack is configured while Turbopack is not, which may cause problems.
 ⚠ See instructions if you need to configure Turbopack:
  https://nextjs.org/docs/app/api-reference/next-config-js/turbopack

[?25h
