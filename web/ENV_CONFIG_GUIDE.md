# K2 前端环境配置指南

## 📋 概述

前端项目现已完全支持通过 `.env.local` 文件进行环境配置，所有服务端点都可以通过环境变量灵活配置。

## 🔧 配置文件说明

### `.env.local` 文件结构

```bash
# 主要 API 配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1

# AI 模型服务
NEXT_PUBLIC_OLLAMA_API_URL=http://localhost:8080/ollama
NEXT_PUBLIC_OPENAI_API_URL=http://localhost:8080/openai

# 媒体和内容服务
NEXT_PUBLIC_AUDIO_API_URL=http://localhost:8080/api/v1/audio
NEXT_PUBLIC_IMAGES_API_URL=http://localhost:8080/api/v1/images
NEXT_PUBLIC_RETRIEVAL_API_URL=http://localhost:8080/api/v1/retrieval

# WebSocket 连接
NEXT_PUBLIC_WS_URL=ws://localhost:8080/ws

# 功能开关
NEXT_PUBLIC_ENABLE_COMMUNITY_SHARING=true
NEXT_PUBLIC_ENABLE_DIRECT_CONNECTIONS=false
```

## 🌐 环境变量详解

### 核心 API 配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `NEXT_PUBLIC_API_BASE_URL` | `http://localhost:8080/api/v1` | 主后端 API 基础路径 |
| `NEXT_PUBLIC_APP_NAME` | `K2` | 应用名称 |
| `NEXT_PUBLIC_APP_VERSION` | `0.1.0` | 应用版本 |

### AI 服务配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `NEXT_PUBLIC_OLLAMA_API_URL` | `http://localhost:8080/ollama` | Ollama 大语言模型服务 |
| `NEXT_PUBLIC_OPENAI_API_URL` | `http://localhost:8080/openai` | OpenAI 兼容 API 服务 |

### 媒体服务配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `NEXT_PUBLIC_AUDIO_API_URL` | `http://localhost:8080/api/v1/audio` | 音频处理服务 |
| `NEXT_PUBLIC_IMAGES_API_URL` | `http://localhost:8080/api/v1/images` | 图像处理服务 |
| `NEXT_PUBLIC_RETRIEVAL_API_URL` | `http://localhost:8080/api/v1/retrieval` | 检索增强生成服务 |

### 实时通信配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `NEXT_PUBLIC_WS_URL` | `ws://localhost:8080/ws` | WebSocket 连接地址 |

### 功能开关配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `NEXT_PUBLIC_ENABLE_COMMUNITY_SHARING` | `true` | 启用社区分享功能 |
| `NEXT_PUBLIC_ENABLE_DIRECT_CONNECTIONS` | `false` | 启用直连功能 |
| `NEXT_PUBLIC_ENABLE_DEBUG_LOGS` | `true` | 启用调试日志 |

### 性能配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `NEXT_PUBLIC_API_TIMEOUT` | `30000` | API 请求超时时间（毫秒） |

## 🚀 使用方法

### 1. 本地开发配置

创建或编辑 `.env.local` 文件：

```bash
# 本地开发 - 所有服务运行在本地
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1
NEXT_PUBLIC_OLLAMA_API_URL=http://localhost:8080/ollama
NEXT_PUBLIC_OPENAI_API_URL=http://localhost:8080/openai
NEXT_PUBLIC_WS_URL=ws://localhost:8080/ws
```

### 2. 远程服务器配置

```bash
# 远程服务器配置
NEXT_PUBLIC_API_BASE_URL=http://your-backend-server.com/api/v1
NEXT_PUBLIC_OLLAMA_API_URL=http://your-backend-server.com/ollama
NEXT_PUBLIC_OPENAI_API_URL=http://your-backend-server.com/openai
NEXT_PUBLIC_WS_URL=ws://your-backend-server.com/ws
```

### 3. 混合配置

```bash
# 混合配置 - 部分服务使用远程，部分使用本地
NEXT_PUBLIC_API_BASE_URL=http://remote-api.com/api/v1
NEXT_PUBLIC_OLLAMA_API_URL=http://localhost:11434  # 本地 Ollama
NEXT_PUBLIC_OPENAI_API_URL=http://remote-openai.com/v1
```

## 🔍 配置验证

### 验证方法

1. 检查浏览器开发者工具的控制台输出
2. 查看网络请求是否指向正确的API端点
3. 确认WebSocket连接状态

### 故障排除

- ✅ 确保 `.env.local` 文件在项目根目录
- ✅ 环境变量必须以 `NEXT_PUBLIC_` 开头
- ✅ 修改环境变量后需要重启开发服务器
- ✅ WebSocket URL 使用 `ws://` 或 `wss://` 协议

## 📱 部署配置

### Vercel 部署

在 Vercel 项目设置中添加环境变量：

```
NEXT_PUBLIC_API_BASE_URL=https://your-api.com/api/v1
NEXT_PUBLIC_OLLAMA_API_URL=https://your-api.com/ollama
NEXT_PUBLIC_WS_URL=wss://your-api.com/ws
```

### Docker 部署

使用 Docker 环境变量：

```dockerfile
ENV NEXT_PUBLIC_API_BASE_URL=http://backend:8080/api/v1
ENV NEXT_PUBLIC_OLLAMA_API_URL=http://backend:8080/ollama
ENV NEXT_PUBLIC_WS_URL=ws://backend:8080/ws
```

## 🔄 配置更新流程

1. **修改配置**：编辑 `.env.local` 文件
2. **重启服务**：`npm run dev` 或 `yarn dev`
3. **验证配置**：检查控制台输出和网络请求
4. **测试功能**：确保所有功能正常工作

## 📚 相关文件

- `src/lib/constants.ts` - 常量定义和环境变量处理
- `src/lib/api/index.ts` - 主要 API 客户端
- `src/lib/api/auth.ts` - 认证 API
- `src/lib/websocket/client.ts` - WebSocket 客户端

## ⚠️ 注意事项

- 🔒 生产环境中的敏感信息应使用安全的环境变量管理
- 🌍 CORS 策略需要在后端正确配置
- 🔄 WebSocket 连接可能需要特殊的代理配置
- 📱 移动端访问时注意使用正确的主机地址

---

现在你的前端项目已完全支持通过环境变量配置所有服务端点！🎉