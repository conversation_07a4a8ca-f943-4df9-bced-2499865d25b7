'use client';

import { useEffect, useCallback } from 'react';
import { useAppStore, useAuthStore } from '@/lib/stores';

export const useWebSocket = () => {
  const { 
    socket, 
    socketClient, 
    isSocketConnected, 
    activeUserIds, 
    usagePool, 
    socketError,
    initializeSocket, 
    disconnectSocket, 
    emitSocketEvent 
  } = useAppStore();
  
  const { token } = useAuthStore();

  // Initialize WebSocket connection
  const connect = useCallback(async () => {
    if (!token) {
      console.warn('No authentication token available for WebSocket connection');
      return;
    }

    if (socketClient && isSocketConnected) {
      console.log('WebSocket already connected');
      return;
    }

    try {
      await initializeSocket(token);
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  }, [token, socketClient, isSocketConnected, initializeSocket]);

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    disconnectSocket();
  }, [disconnectSocket]);

  // Emit event
  const emit = useCallback((event: string, data: any) => {
    emitSocketEvent(event, data);
  }, [emitSocketEvent]);

  // Subscribe to events
  const on = useCallback((event: string, handler: (...args: any[]) => void) => {
    if (socketClient) {
      socketClient.on(event, handler);
    }
  }, [socketClient]);

  // Unsubscribe from events
  const off = useCallback((event: string, handler?: (...args: any[]) => void) => {
    if (socketClient) {
      socketClient.off(event, handler);
    }
  }, [socketClient]);

  // Don't auto-connect - let components decide when to connect
  // useEffect(() => {
  //   if (token && !isSocketConnected && !socketClient) {
  //     connect();
  //   }
  // }, [token, isSocketConnected, socketClient, connect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Don't disconnect on unmount as other components might be using it
      // disconnect();
    };
  }, []);

  return {
    socket,
    socketClient,
    isConnected: isSocketConnected,
    activeUserIds,
    usagePool,
    error: socketError,
    connect,
    disconnect,
    emit,
    on,
    off,
  };
};

// Hook for channel-specific WebSocket operations
export const useChannelSocket = (channelId?: string) => {
  const webSocket = useWebSocket();

  // Join channel
  const joinChannel = useCallback(() => {
    if (channelId && webSocket.isConnected) {
      webSocket.emit('join-channel', { channel_id: channelId });
    }
  }, [channelId, webSocket]);

  // Leave channel
  const leaveChannel = useCallback(() => {
    if (channelId && webSocket.isConnected) {
      webSocket.emit('leave-channel', { channel_id: channelId });
    }
  }, [channelId, webSocket]);

  // Send typing indicator
  const sendTyping = useCallback((threadId?: string) => {
    if (channelId && webSocket.isConnected) {
      webSocket.emit('channel-events', {
        channel_id: channelId,
        message_id: threadId,
        data: { type: 'typing' }
      });
    }
  }, [channelId, webSocket]);

  // Send channel event
  const sendChannelEvent = useCallback((data: any) => {
    if (channelId && webSocket.isConnected) {
      webSocket.emit('channel-events', {
        channel_id: channelId,
        data
      });
    }
  }, [channelId, webSocket]);

  // Subscribe to channel events
  const onChannelEvent = useCallback((handler: (data: any) => void) => {
    webSocket.on('channel-events', (data) => {
      if (data.channel_id === channelId) {
        handler(data);
      }
    });
  }, [channelId, webSocket]);

  // Connect WebSocket when channel is accessed and join/leave channel
  useEffect(() => {
    if (channelId && webSocket.token && !webSocket.isConnected) {
      console.log('Connecting WebSocket for channel functionality');
      webSocket.connect().catch(console.error);
    }
    
    if (channelId && webSocket.isConnected) {
      joinChannel();
      
      return () => {
        leaveChannel();
      };
    }
  }, [channelId, webSocket.token, webSocket.isConnected, webSocket.connect, joinChannel, leaveChannel]);

  return {
    ...webSocket,
    joinChannel,
    leaveChannel,
    sendTyping,
    sendChannelEvent,
    onChannelEvent,
  };
};

// Hook for chat-specific WebSocket operations
export const useChatSocket = () => {
  const webSocket = useWebSocket();

  // Send chat message
  const sendChatMessage = useCallback(async (data: {
    chatId: string;
    messageId: string;
    message: any;
    models: string[];
    files?: any[];
  }) => {
    if (!webSocket.isConnected || !webSocket.socketClient) {
      throw new Error('WebSocket not connected');
    }
    
    return webSocket.socketClient.sendChatMessage(data);
  }, [webSocket]);

  // Send chat event
  const sendChatEvent = useCallback((data: any) => {
    if (webSocket.isConnected) {
      webSocket.emit('chat-events', data);
    }
  }, [webSocket]);

  // Subscribe to chat events
  const onChatEvent = useCallback((handler: (data: any) => void) => {
    if (webSocket.socketClient) {
      webSocket.socketClient.onChatEvent(handler);
    }
  }, [webSocket]);

  // Unsubscribe from chat events
  const offChatEvent = useCallback((handler?: (data: any) => void) => {
    if (webSocket.socketClient) {
      webSocket.socketClient.offChatEvent(handler);
    }
  }, [webSocket]);

  // Send model usage
  const sendUsage = useCallback((modelId: string) => {
    if (webSocket.isConnected) {
      webSocket.emit('usage', { model: modelId });
    }
  }, [webSocket]);

  // Join user session (for authentication)
  const joinUserSession = useCallback(async (token: string) => {
    if (!webSocket.isConnected || !webSocket.socketClient) {
      throw new Error('WebSocket not connected');
    }
    
    return webSocket.socketClient.joinUserSession(token);
  }, [webSocket]);

  return {
    ...webSocket,
    sendChatMessage,
    sendChatEvent,
    onChatEvent,
    offChatEvent,
    sendUsage,
    joinUserSession,
  };
};

// Hook for user presence
export const useUserPresence = () => {
  const { activeUserIds } = useAppStore();
  const webSocket = useWebSocket();

  // Check if user is online
  const isUserOnline = useCallback((userId: string) => {
    return activeUserIds.includes(userId);
  }, [activeUserIds]);

  // Get online users count
  const onlineUsersCount = activeUserIds.length;

  return {
    activeUserIds,
    onlineUsersCount,
    isUserOnline,
    isConnected: webSocket.isConnected,
  };
};
