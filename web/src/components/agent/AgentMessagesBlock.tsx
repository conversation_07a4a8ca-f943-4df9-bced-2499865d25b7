"use client";

import { useCallback, useRef, useState } from "react";
import { useAgentStore, sendMessage, useMessageIds, useResponding } from "@/lib/agent/store";
import type { Option, Resource } from "@/lib/agent/types";
import { cn } from "@/lib/utils";

import { AgentInputBox, type AgentInputBoxRef } from "./AgentInputBox";
import { AgentMessageList } from "./AgentMessageList";
import { AgentWelcome } from "./AgentWelcome";

export interface AgentMessagesBlockProps {
  className?: string;
}

export function AgentMessagesBlock({ className }: AgentMessagesBlockProps) {
  const messageIds = useMessageIds();
  const messageCount = messageIds.length;
  const responding = useResponding();
  const abortControllerRef = useRef<AbortController | null>(null);
  const [feedback, setFeedback] = useState<{ option: Option } | null>(null);
  const inputBoxRef = useRef<AgentInputBoxRef>(null);

  const handleFillInput = useCallback((content: string) => {
    if (inputBoxRef.current) {
      inputBoxRef.current.fillContent(content);
    }
  }, []);

  const handleSend = useCallback(
    async (
      message: string,
      options?: {
        interruptFeedback?: string;
        resources?: Array<Resource>;
        tools?: Array<string>;
      },
    ) => {
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      
      try {
        await sendMessage(
          message,
          {
            interruptFeedback:
              options?.interruptFeedback ?? feedback?.option.value,
            resources: options?.resources,
            enabledTools: options?.tools,
          },
          {
            abortSignal: abortController.signal,
          },
        );
      } catch (error) {
        console.error("Error sending message:", error);
      }
    },
    [feedback],
  );

  const handleCancel = useCallback(() => {
    abortControllerRef.current?.abort();
    abortControllerRef.current = null;
  }, []);

  const handleFeedback = useCallback(
    (feedback: { option: Option }) => {
      setFeedback(feedback);
    },
    [setFeedback],
  );

  const handleRemoveFeedback = useCallback(() => {
    setFeedback(null);
  }, [setFeedback]);

  return (
    <div className={cn("flex h-full flex-col", className)}>
      <AgentMessageList
        className="flex flex-grow"
        onFeedback={handleFeedback}
        onSendMessage={handleSend}
      />
      
      <div className={cn(
        !responding && messageCount === 0 && "absolute top-[calc(50%-150px)]",
        "flex flex-col shrink-0 pb-4 w-full"
      )}>
        {!responding && messageCount === 0 && (
          <AgentWelcome 
            className="mb-6"
            onSend={handleSend}
            onFillInput={handleFillInput}
          />
        )}
        
        <AgentInputBox
          ref={inputBoxRef}
          className="w-full"
          responding={responding}
          feedback={feedback}
          onSend={handleSend}
          onCancel={handleCancel}
          onRemoveFeedback={handleRemoveFeedback}
        />
      </div>
    </div>
  );
}