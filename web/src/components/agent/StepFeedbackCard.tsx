import { Alert<PERSON>riangle } from "lucide-react";
import React from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface StepFeedbackCardProps {
  stepTitle: string;
  stepDescription: string;
  stepType: "research" | "processing";
  executionResult: string;
  hasErrors?: boolean;
  errorReasons?: string[];
  errorSummary?: string;
  onFeedback: (feedback: string) => void;
}

export function StepFeedbackCard({
  stepTitle,
  stepDescription,
  stepType,
  executionResult,
  hasErrors,
  errorReasons,
  errorSummary,
  onFeedback,
}: StepFeedbackCardProps) {
  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span className="text-lg font-semibold">Step Feedback</span>
          <span className="text-sm text-muted-foreground">
            {stepType === "research" ? "🔍 Research" : "⚙️ Processing"}
          </span>
          {hasErrors && (
            <div className="flex items-center gap-1 ml-auto text-red-600">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm font-medium">Error Detected</span>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium text-sm text-muted-foreground">Step Title</h3>
          <p className="text-base">{stepTitle}</p>
        </div>
        <div>
          <h3 className="font-medium text-sm text-muted-foreground">Step Description</h3>
          <p className="text-base">{stepDescription}</p>
        </div>
        
        {hasErrors && (
          <div className="p-4 rounded-md bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800">
            <div className="flex items-center gap-2 text-red-800 dark:text-red-200 mb-3">
              <AlertTriangle className="w-4 h-4" />
              <span className="font-medium">Potential issues detected. Please review carefully.</span>
            </div>
            
            {errorReasons && errorReasons.length > 0 && (
              <div className="mb-3">
                <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">Detected Issues:</h4>
                <ul className="list-disc list-inside space-y-1">
                  {errorReasons.map((reason, index) => (
                    <li key={index} className="text-sm text-red-700 dark:text-red-300">{reason}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {errorSummary && (
              <div>
                <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">Error Details:</h4>
                <div className="text-sm text-red-700 dark:text-red-300 whitespace-pre-line">
                  {errorSummary}
                </div>
              </div>
            )}
          </div>
        )}
        
        <div>
          <h3 className="font-medium text-sm text-muted-foreground">Execution Result</h3>
          <div className="mt-2 p-4 bg-muted rounded-lg">
            <div className="prose prose-sm max-w-none">
              {executionResult}
            </div>
          </div>
        </div>
        
        <div className="flex gap-2 pt-4">
          <Button
            onClick={() => onFeedback("[STEP_ACCEPTED]")}
            className="flex-1 bg-green-600 hover:bg-green-700"
          >
            ✅ Accept Step
          </Button>
          <Button
            onClick={() => onFeedback("[STEP_NEEDS_MORE_WORK]")}
            className="flex-1 bg-yellow-600 hover:bg-yellow-700"
          >
            🔄 Needs More Work
          </Button>
          <Button
            onClick={() => onFeedback("[STEP_REDO]")}
            className="flex-1 bg-red-600 hover:bg-red-700"
          >
            🔁 Redo Step
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
