import React, { useMemo } from "react";
import type { ToolCallRuntime } from "@/lib/agent/types";

// Hotel data types
interface Hotel {
  name: string;
  overall_rating: number;
  link: string;
  thumbnail: string;
  price: string;
  reviews: number;
}

const HotelToolCard = ({ toolCall }: { toolCall: ToolCallRuntime }) => {
  const results: any = useMemo(() => {
    try {
      return JSON.parse(toolCall.result || '{}');
    } catch {
      return {};
    }
  }, [toolCall.result]);

  const fullStars = Math.floor(results?.ads?.overall_rating || 0);
  const halfStar = (results?.ads?.overall_rating || 0) - fullStars >= 0.5;
  const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);

  return (
    <section className="w-full flex justify-center">
      <div className="w-full max-w-md p-[16px] flex flex-col gap-[16px] rounded-xl border border-[#E8E9FF] bg-[#fff]">
        {/* Top badge */}
        <div className="flex items-center gap-[2px] p-[4px] rounded-sm border border-[#EFEFF3]" style={{ width: "fit-content" }}>
          <img src="/images/hotel-icon.svg" alt="hotel" className="w-[16px] h-[16px]" />
          <span className="text-[12px] text-[#101828]">Hotel</span>
        </div>
        {/* Hotel cards list */}
        <div className="flex flex-col gap-[8px]">
          {results?.ads?.map((hotel: Hotel, idx: number) => (
            <a
              key={idx}
              href={hotel?.link}
              target="_blank"
              rel="noopener noreferrer"
              className="block group"
              style={{ textDecoration: "none" }}
            >
              <div className="flex justify-between gap-[8px] rounded-lg border border-[#EFEFF3] shadow-md hover:shadow-lg transition-shadow duration-200 p-[12px] cursor-pointer">
                <div className="flex-1 min-w-0 flex flex-col justify-between">
                  <div className="w-full">
                    <div className="text-sm text-[#101828] truncate leading-[18px]">
                      {hotel?.name}
                    </div>
                    {/* Star rating */}
                    <div className="flex items-center gap-[2px] mt-[4px] h-[15px] leading-[15px]">
                      <span className="text-xs text-[#676F83]">{hotel?.overall_rating}/5</span>
                      <span className="flex items-center ml-[4px]">
                        {Array.from({ length: fullStars }).map((_, i) => (
                          <img src="/images/star-active-icon.svg" key={i} className="w-[12px] h-[12px]" />
                        ))}
                        {halfStar && <img src="/images/star-active-icon.svg" className="w-[12px] h-[12px]" />}
                        {Array.from({ length: emptyStars }).map((_, i) => (
                          <img src="/images/star-inactive-icon.svg" key={i} className="w-[12px] h-[12px]" />
                        ))}
                      </span>
                      <span className="text-xs text-[#676F83] ml-[4px]">
                        ({(hotel?.reviews / 1000).toFixed(1)}k)
                      </span>
                    </div>
                  </div>
                  <div className="text-sm text-[#101828] leading-[18px]">${hotel?.price}</div>
                </div>
                {hotel?.thumbnail && (
                  <img
                    src={hotel?.thumbnail}
                    alt={hotel?.name}
                    className="w-[70px] h-[70px] object-cover rounded-sm flex-shrink-0"
                  />
                )}
              </div>
            </a>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HotelToolCard;
