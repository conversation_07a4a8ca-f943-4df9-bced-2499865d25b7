import React, { useMemo } from "react";
import type { ToolCallRuntime } from "@/lib/agent/types";

interface EventItem {
  thumbnail: string;
  link: string;
  title: string;
  description: string;
  date: {
    when: string;
  };
}

interface EventsData {
  events?: EventItem[];
}

export default function EventToolCard({ toolCall }: { toolCall: ToolCallRuntime }) {
  // Parse event data
  const eventsData: EventsData = useMemo(() => {
    try {
      return JSON.parse(toolCall.result || '{}');
    } catch {
      return {};
    }
  }, [toolCall.result]);

  let events: EventItem[] = [];
  
  if (eventsData?.events) {
    events = eventsData.events;
  } else {
    events = [];
  }

  // Date formatting
  const formatDate = (dateStr: string) => {
    // Handle "Sat, Aug 9, 1 – 5 PM GMT+8" format
    const match = /(\w+),\s+(\w+)\s+(\d+)/.exec(dateStr);
    if (match) {
      const [, , month, day] = match;
      return { day, month: month?.toUpperCase() ?? "???" };
    }
    
    // If format doesn't match, try parsing as Date object
    try {
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        const day = date.getDate();
        const month = date.toLocaleString("en-US", { month: "short" }).toUpperCase();
        return { day, month };
      }
    } catch {
      // If parsing fails, return default values
    }
    
    return { day: "?", month: "???" };
  };
  
  return (
    <section className="w-full flex justify-center">
      <div className="w-full max-w-md p-[16px] flex flex-col gap-[16px] rounded-xl border border-[#E8E9FF] bg-[#fff]">
        {/* Top label */}
        <div className="flex items-center gap-[2px] p-[4px] rounded-sm border border-[#EFEFF3]" style={{ width: "fit-content" }}>
          <img src="/images/event-icon.svg" alt="event" className="w-[16px] h-[16px]" />
          <span className="text-[12px] text-[#101828]">Event</span>
        </div>
        <div className="flex flex-col gap-[8px]">
          {events.map((event, idx) => {
            const { day, month } = formatDate(event?.date?.when || "");
            return (
              <a
                key={idx}
                href={event.link}
                target="_blank"
                rel="noopener noreferrer"
                className="block group"
                style={{ textDecoration: "none" }}
              >
                <div className="flex justify-between gap-[12px] rounded-lg border border-[#EFEFF3] shadow-md hover:shadow-lg transition-shadow duration-200 p-[12px] cursor-pointer">
                  <div className="flex flex-col items-center text-[#5C62FF] ml-[4px]">
                    <div className="text-2xl font-bold leading-[24px]">{day}</div>
                    <div className="text-xs uppercase">{month}</div>
                  </div>
                  <div className="flex-1 max-w-[219px]">
                    <h2
                      className="text-sm text-[#101828] leading-[18px] break-words line-clamp-2"
                      style={{
                        display: "-webkit-box",
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: "vertical",
                        overflow: "hidden",
                      }}
                    >
                      {event?.title}
                    </h2>
                    <p
                      className="text-xs text-[#676F83] leading-[15px] line-clamp-2 mt-[4px]"
                      style={{
                        display: "-webkit-box",
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: "vertical",
                        overflow: "hidden",
                      }}
                    >
                      {event?.description}
                    </p>
                  </div>
                  {event?.thumbnail && (
                    <img
                      src={event.thumbnail}
                      alt={event.title}
                      className="w-[70px] h-[70px] object-cover rounded-sm flex-shrink-0"
                    />
                  )}
                </div>
              </a>
            );
          })}
        </div>
      </div>
    </section>
  );
}
