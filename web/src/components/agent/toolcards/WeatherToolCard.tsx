import React, { useMemo } from 'react';
import type { ToolCallRuntime } from '@/lib/agent/types';

interface WeatherForecastItem {
  formatted_date: string;
  maxTemperature: {
    degrees: number;
    unit: string;
  };
  minTemperature: {
    degrees: number;
    unit: string;
  };
  daytimeForecast: {
    weatherCondition: {
      description: {
        text: string;
      };
      iconBaseUri: string;
    };
  };
}

interface WeatherData {
  formatted_address?: string;
  forecast_data?: WeatherForecastItem[];
}

export default function WeatherToolCard({ toolCall }: { toolCall: ToolCallRuntime }) {
  // Try to parse weather data
  let weatherData: WeatherData | null = null;

  try {
    weatherData = JSON.parse(toolCall.result || '{}');
  } catch {
    weatherData = null;
  }

  // Tool badge
  const toolBadge = (
    <div className="flex items-center gap-[4px] p-[4px] rounded-sm border border-[#EFEFF3] w-fit">
      <img src="/images/weather-icon.svg" className="w-[16px] h-[16px]" />
      <span className="text-[12px] text-[#101828]">Weather</span>
    </div>
  );

  return (
    <section className="w-full flex justify-center">
      <div className="w-full max-w-md p-[16px] flex flex-col gap-[16px] rounded-xl border border-[#E8E9FF] bg-[#fff]">
        {/* Tool badge */}
        {toolBadge}
        {/* Weather description */}
        {weatherData?.formatted_address && (
          <div className="text-[14px] text-[#101828]">{weatherData.formatted_address}</div>
        )}
        {/* Weather forecast cards */}
        {Array.isArray(weatherData?.forecast_data) && (
          <div className="flex flex-col gap-[8px]">
            {weatherData?.forecast_data?.map((item, idx) => (
              <div
                key={idx}
                className="flex items-center gap-[12px] p-[12px] rounded-lg border border-[#EFEFF3] text-[#101828] text-sm"
              >
                <img
                  src={item?.daytimeForecast?.weatherCondition?.iconBaseUri ?? "/images/cloudy.svg"}
                  alt={item?.daytimeForecast?.weatherCondition?.description?.text}
                  className="w-[24px] h-[24px] object-contain"
                />
                <div className="font-bold flex-1">
                  {item?.formatted_date}
                </div>
                <div className="flex-1">
                  {item?.minTemperature?.degrees} - {item?.maxTemperature?.degrees} °C
                </div>
                <div
                  className="flex-[2] line-clamp-2 w-[100px]"
                  style={{
                    display: "webkit-box",
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: "vertical",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "normal",
                  }}
                >
                  {item?.daytimeForecast?.weatherCondition?.description?.text}
                </div>
              </div>
            ))}
          </div>
        )}
        {/* Fallback for other content */}
        {weatherData?.forecast_data?.length === 0 && (
          <div className="prose prose-sm max-w-none mt-2 overflow-hidden">
            <div className="overflow-x-auto">
              {toolCall.result}
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
