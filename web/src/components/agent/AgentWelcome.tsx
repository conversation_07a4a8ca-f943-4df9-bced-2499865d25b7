import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export function AgentWelcome({ className }: { className?: string }) {
  return (
    <motion.div
      className={cn("flex flex-col", className)}
      style={{ transition: "all 0.2s ease-out" }}
      initial={{ opacity: 0, scale: 0.85 }}
      animate={{ opacity: 1, scale: 1 }}
    >
      <h3 className="mb-2 text-center text-3xl font-medium -mt-4">
        👋 Hello, there! How can I help you?
      </h3>
    </motion.div>
  );
}
