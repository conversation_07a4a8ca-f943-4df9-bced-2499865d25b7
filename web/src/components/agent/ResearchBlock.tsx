import { Check, Copy, X, Download } from "lucide-react";
import { useCallback, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

export function ResearchBlock({
  className,
  researchId = null,
}: {
  className?: string;
  researchId: string | null;
}) {
  const [activeTab, setActiveTab] = useState("activities");
  const [copied, setCopied] = useState(false);
  const [editing, setEditing] = useState(false);

  const handleCopy = useCallback(() => {
    // Mock copy functionality
    navigator.clipboard.writeText("Research report content");
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 1000);
  }, []);

  const handleDownload = useCallback(() => {
    // Mock download functionality
    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-');
    const filename = `research-report-${timestamp}.md`;
    const blob = new Blob(["Research report content"], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 0);
  }, []);

  const handleEdit = useCallback(() => {
    setEditing((editing) => !editing);
  }, []);

  if (!researchId) {
    return null;
  }

  return (
    <div className={cn("h-full w-full overflow-hidden", className)}>
      <Card className={cn("relative h-full w-full pt-4", className)}>
        <div className="absolute right-4 flex h-9 items-center justify-center">
          <Button
            className="text-gray-400"
            size="icon"
            variant="ghost"
            onClick={handleCopy}
          >
            {copied ? <Check /> : <Copy />}
          </Button>
          <Button
            className="text-gray-400"
            size="icon"
            variant="ghost"
            onClick={handleDownload}
          >
            <Download />
          </Button>
          <Button
            className="text-gray-400"
            size="sm"
            variant="ghost"
            onClick={() => {
              // Close research - this would be handled by parent
            }}
          >
            <X />
          </Button>
        </div>
        <Tabs
          className="flex h-full w-full flex-col"
          value={activeTab}
          onValueChange={(value) => setActiveTab(value)}
        >
          <div className="flex w-full justify-center">
            <TabsList className="">
              <TabsTrigger className="px-8" value="report">
                Report
              </TabsTrigger>
              <TabsTrigger className="px-8" value="activities">
                Activities
              </TabsTrigger>
            </TabsList>
          </div>
          <TabsContent
            className="h-full min-h-0 flex-grow px-8"
            value="report"
            forceMount
            hidden={activeTab !== "report"}
          >
            <div className="h-full overflow-y-auto">
              <div className="w-full pt-4 pb-8">
                <div className="prose prose-sm max-w-none">
                  <h2>Research Report</h2>
                  <p>This is a placeholder for the research report content.</p>
                  <p>The actual report would be generated by the AI agent and displayed here.</p>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent
            className="h-full min-h-0 flex-grow px-8 bg-[#EFEFF3]"
            value="activities"
            forceMount
            hidden={activeTab !== "activities"}
          >
            <div className="h-full overflow-y-auto">
              <div className="max-w-[600px] py-4">
                <div className="text-sm text-gray-600 mb-4">
                  Research activities will be displayed here as they happen.
                </div>
                <div className="space-y-4">
                  <div className="bg-white rounded-lg p-4 border">
                    <div className="text-sm font-medium mb-2">🔍 Web Search</div>
                    <div className="text-xs text-gray-500">Searching for relevant information...</div>
                  </div>
                  <div className="bg-white rounded-lg p-4 border">
                    <div className="text-sm font-medium mb-2">📊 Data Analysis</div>
                    <div className="text-xs text-gray-500">Processing and analyzing data...</div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
}
