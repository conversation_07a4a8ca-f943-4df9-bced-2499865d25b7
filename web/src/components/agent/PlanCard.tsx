import { Ch<PERSON>ronDown, ChevronRight, Lightbulb } from "lucide-react";
import { motion } from "framer-motion";
import React, { useCallback, useMemo, useState } from "react";

import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import type { Message, Option } from "@/lib/agent/types";
import { cn } from "@/lib/utils";

function ThoughtBlock({
  className,
  content,
  isStreaming,
  hasMainContent,
}: {
  className?: string;
  content: string;
  isStreaming?: boolean;
  hasMainContent?: boolean;
}) {
  const [isOpen, setIsOpen] = useState(true);
  const [hasAutoCollapsed, setHasAutoCollapsed] = useState(false);

  React.useEffect(() => {
    if (hasMainContent && !hasAutoCollapsed) {
      setIsOpen(false);
      setHasAutoCollapsed(true);
    }
  }, [hasMainContent, hasAutoCollapsed]);

  if (!content || content.trim() === "") {
    return null;
  }

  return (
    <div className={cn("mb-6 w-full", className)}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "h-auto w-full justify-start rounded-xl border px-6 py-4 text-left transition-all duration-200",
              "hover:bg-accent hover:text-accent-foreground",
              isStreaming
                ? "border-primary/20 bg-primary/5 shadow-sm"
                : "border-border bg-card",
            )}
          >
            <div className="flex w-full items-center gap-3">
              <Lightbulb
                size={18}
                className={cn(
                  "shrink-0 transition-colors duration-200",
                  isStreaming ? "text-primary" : "text-muted-foreground",
                )}
              />
              <span
                className={cn(
                  "leading-none font-semibold transition-colors duration-200",
                  isStreaming ? "text-primary" : "text-foreground",
                )}
              >
                Deep Thinking
              </span>
              {isStreaming && (
                <div className="ml-2 scale-75 flex space-x-1">
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              )}
              <div className="flex-grow" />
              {isOpen ? (
                <ChevronDown
                  size={16}
                  className="text-muted-foreground transition-transform duration-200"
                />
              ) : (
                <ChevronRight
                  size={16}
                  className="text-muted-foreground transition-transform duration-200"
                />
              )}
            </div>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:slide-up-2 data-[state=open]:slide-down-2 mt-3">
          <Card
            className={cn(
              "transition-all duration-200",
              isStreaming ? "border-primary/20 bg-primary/5" : "border-border",
            )}
          >
            <CardContent>
              <div className="flex h-40 w-full overflow-y-auto">
                <div className="flex h-full w-full flex-col overflow-hidden">
                  <div
                    className={cn(
                      "prose dark:prose-invert max-w-none transition-colors duration-200",
                      isStreaming ? "prose-primary" : "opacity-80",
                    )}
                  >
                    {content}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}

const GREETINGS = ["Cool", "Sounds great", "Looks good", "Great", "Awesome"];

export function PlanCard({
  className,
  message,
  interruptMessage,
  onFeedback,
  waitForFeedback,
  onSendMessage,
}: {
  className?: string;
  message: Message;
  interruptMessage?: Message | null;
  onFeedback?: (feedback: { option: Option }) => void;
  onSendMessage?: (
    message: string,
    options?: { interruptFeedback?: string },
  ) => void;
  waitForFeedback?: boolean;
}) {
  const plan = useMemo<{
    title?: string;
    thought?: string;
    steps?: { title?: string; description?: string }[];
    clarification_questions?: string[];
    clarification_questions_with_options?: Array<{
      question: string;
      options: Array<{ text: string; value: string; description?: string }>;
      allow_custom_input: boolean;
      required: boolean;
    }>;
  }>(() => {
    try {
      return JSON.parse(message.content ?? "{}");
    } catch {
      return {};
    }
  }, [message.content]);

  const reasoningContent = message.reasoningContent;
  const hasMainContent = Boolean(
    message.content && message.content.trim() !== "",
  );
  const isThinking = Boolean(reasoningContent && !hasMainContent);
  const shouldShowPlan = hasMainContent;

  // Clarification options local state
  const [selectedOptions, setSelectedOptions] = useState<Record<number, string[]>>({});
  const [customInputs, setCustomInputs] = useState<Record<number, string>>({});
  const [additionalRequirements, setAdditionalRequirements] = useState<string>("");

  // Option click toggle (single selection, supports deselection)
  function handleOptionClick(qIdx: number, oValue: string) {
    setSelectedOptions(prev => {
      const currentSelection = prev[qIdx] ?? [];
      // If current option is already selected, deselect it
      if (currentSelection.includes(oValue)) {
        return {
          ...prev,
          [qIdx]: []
        };
      } else {
        // Otherwise select current option (single selection)
        return {
          ...prev,
          [qIdx]: [oValue]
        };
      }
    });
  }

  // Custom input
  function handleCustomInput(qIdx: number, value: string) {
    setCustomInputs(prev => ({ ...prev, [qIdx]: value }));
  }

  // Check if any option is selected or has custom input or additional requirements
  const hasAnySelection = useMemo(() => {
    return (
      Object.values(selectedOptions).some(arr => arr.length > 0) ||
      Object.values(customInputs).some(v => v && v.trim() !== "") ||
      (additionalRequirements && additionalRequirements.trim() !== "")
    );
  }, [selectedOptions, customInputs, additionalRequirements]);

  // Assemble supplement info
  function getSupplementInfo() {
    const arr: string[] = [];
    plan.clarification_questions_with_options?.forEach((q, qIdx) => {
      // Options
      (selectedOptions[qIdx] ?? []).forEach(val => {
        const opt = q.options.find(o => o.value === val);
        if (opt) arr.push(`${q.question}: ${opt.text}`);
      });
      // Custom
      if (customInputs[qIdx] && customInputs[qIdx].trim() !== "") {
        arr.push(`${q.question}: ${customInputs[qIdx]}`);
      }
    });
    // Add additional requirements
    if (additionalRequirements && additionalRequirements.trim() !== "") {
      arr.push(`Additional requirements: ${additionalRequirements}`);
    }
    return arr.join("\n");
  }

  // Edit button click
  function handleEdit() {
    const supplement = getSupplementInfo();
    if (supplement && onSendMessage) {
      onSendMessage(`[CLARIFICATION_RESPONSE] ${supplement}`, { interruptFeedback: "clarification_response" });
    }
  }

  // Start Search button click
  function handleStartSearch() {
    if (onSendMessage) {
      onSendMessage("[START_SEARCH]", { interruptFeedback: "accepted" });
    }
  }

  const handleAccept = useCallback(async () => {
    if (onSendMessage) {
      onSendMessage(
        `${GREETINGS[Math.floor(Math.random() * GREETINGS.length)]}! ${Math.random() > 0.5 ? "Let's get started." : "Let's start."}`,
        {
          interruptFeedback: "accepted",
        },
      );
    }
  }, [onSendMessage]);

  return (
    <div className={cn("w-full", className)}>
      {reasoningContent && (
        <ThoughtBlock
          content={reasoningContent}
          isStreaming={isThinking}
          hasMainContent={hasMainContent}
        />
      )}
      {shouldShowPlan && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          <Card className="w-full">
            <CardHeader>
              <CardTitle>
                <div className="prose prose-sm max-w-none">
                  {`### ${plan.title !== undefined && plan.title !== "" ? plan.title : "Deep Research"}`}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="opacity-80 prose prose-sm max-w-none">
                {plan.thought}
              </div>
              {plan.steps && plan.steps.length > 0 && (
                <ul className="my-2 flex list-decimal flex-col gap-4 border-l-[2px] pl-8">
                  {plan.steps.map((step, i) => (
                    <li key={`step-${i}`}>
                      <h3 className="mb text-lg font-medium">
                        <div className="prose prose-sm max-w-none">{step.title}</div>
                      </h3>
                      <div className="text-muted-foreground text-sm">
                        <div className="prose prose-sm max-w-none">{step.description}</div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
              {/* Clarification questions with options */}
              {plan.clarification_questions_with_options && plan.clarification_questions_with_options.length > 0 && plan.clarification_questions_with_options.every(q => q.options) && (
                <div className="mt-4 p-4 rounded-md bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800">
                  <h4 className="font-semibold mb-3 text-blue-900 dark:text-blue-100">
                    To get more accurate results, please provide additional information:
                  </h4>
                  <div className="space-y-4">
                    {plan.clarification_questions_with_options.map((question, qIdx) => (
                      <div key={qIdx} className="space-y-2">
                        <h5 className="font-medium text-blue-900 dark:text-blue-100">
                          {question.question}
                          {question.required && <span className="text-red-500 ml-1">*</span>}
                        </h5>
                        <div className="flex flex-col gap-2">
                          {question.options?.map((option, oIdx) => {
                            const selected = selectedOptions[qIdx]?.includes(option.value);
                            return (
                              <Button
                                key={oIdx}
                                variant={selected ? "default" : "outline"}
                                className={cn("justify-start text-left h-auto py-2 px-3", selected && "ring-2 ring-blue-400")}
                                onClick={() => handleOptionClick(qIdx, option.value)}
                              >
                                <div className="flex flex-col items-start">
                                  <span className="font-medium">{option.text}</span>
                                  {option.description && (
                                    <span className="text-xs text-muted-foreground mt-1">{option.description}</span>
                                  )}
                                </div>
                              </Button>
                            );
                          })}
                        </div>
                        {question.allow_custom_input && selectedOptions[qIdx]?.includes("other") && (
                          <div className="mt-2">
                            <input
                              type="text"
                              placeholder="Please specify your answer..."
                              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                              value={customInputs[qIdx] ?? ""}
                              onChange={e => handleCustomInput(qIdx, e.target.value)}
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  <div className="flex flex-col gap-2 mt-4">
                    <div className="mt-2">
                      <input
                        type="text"
                        placeholder="Enter any additional requirements or preferences..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                        value={additionalRequirements}
                        onChange={e => setAdditionalRequirements(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}
              {/* Traditional clarification questions (backward compatibility) */}
              {(!plan.clarification_questions_with_options || plan.clarification_questions_with_options.length === 0) && 
               plan.clarification_questions && plan.clarification_questions.length > 0 && (
                <div className="mt-4 p-3 rounded-md bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800">
                  <h4 className="font-semibold mb-2 text-yellow-900 dark:text-yellow-100">
                    AI suggests that to get more accurate results, please provide some additional information:
                  </h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {plan.clarification_questions?.map((question, idx) => (
                      <li key={idx} className="text-sm text-yellow-900 dark:text-yellow-100">
                        {question}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-end">
              {!message.isStreaming && interruptMessage?.options?.length && (
                <motion.div
                  className="flex gap-2"
                  initial={{ opacity: 0, y: 12 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                >
                  {interruptMessage?.options?.map((option) => (
                    option.value !== "edit_plan" &&
                    <Button
                      key={option.value}
                      variant={
                        option.value === "accepted" ? "default" : "outline"
                      }
                      disabled={!waitForFeedback}
                      onClick={() => {
                        if (option.value === "accepted") {
                          void handleAccept();
                        } else {
                          onFeedback?.({
                            option,
                          });
                        }
                      }}
                    >
                      {option.text}
                    </Button>
                  ))}
                </motion.div>
              )}
            </CardFooter>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
