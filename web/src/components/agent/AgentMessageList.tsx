import { motion } from "framer-motion";
import { use<PERSON>allback, useMemo, useRef } from "react";

import { StepFeedbackCard } from "./StepFeedbackCard";
import { PlanCard } from "./PlanCard";
import { AgentWelcome } from "./AgentWelcome";
import WeatherToolCard from "./toolcards/WeatherToolCard";
import HotelToolCard from "./toolcards/HotelToolCard";
import EventToolCard from "./toolcards/EventToolCard";
import TimezoneToolCard from "./toolcards/TimezoneToolCard";

import type { Message, Option, ToolCallRuntime } from "@/lib/agent/types";
import { cn } from "@/lib/utils";

interface MessageListItemProps {
  className?: string;
  message: Message;
  waitForFeedback?: boolean;
  interruptMessage?: Message | null;
  onFeedback?: (feedback: { option: Option }) => void;
  onSendMessage?: (
    message: string,
    options?: { interruptFeedback?: string },
  ) => void;
}

function MessageBubble({
  className,
  message,
  children,
}: {
  className?: string;
  message: Message;
  children: React.ReactNode;
}) {
  return (
    <div
      className={cn(
        `group flex w-fit max-w-[85%] flex-col rounded-2xl px-4 py-3 text-nowrap shadow`,
        message.role === "user" && "bg-brand rounded-ee-none",
        message.role === "assistant" && "bg-card rounded-es-none",
        className,
      )}
    >
      {children}
    </div>
  );
}

// Tool call components
function ToolCallDisplay({ toolCall }: { toolCall: ToolCallRuntime }) {
  if (toolCall.name === "web_search") {
    return <WebSearchToolCall toolCall={toolCall} />;
  } else if (toolCall.name.includes("weather")) {
    return <WeatherToolCard toolCall={toolCall} />;
  } else if (toolCall.name.includes("time")) {
    return <TimezoneToolCard toolCall={toolCall} />;
  } else if (toolCall.name.includes("events")) {
    return <EventToolCard toolCall={toolCall} />;
  } else if (toolCall.name.includes("hotels")) {
    return <HotelToolCard toolCall={toolCall} />;
  } else if (toolCall.name === "crawl_tool") {
    return <CrawlToolCall toolCall={toolCall} />;
  } else if (toolCall.name === "python_repl_tool") {
    return <PythonToolCall toolCall={toolCall} />;
  } else if (toolCall.name === "local_search_tool") {
    return <RetrieverToolCall toolCall={toolCall} />;
  } else {
    return <GenericToolCall toolCall={toolCall} />;
  }
}

// Simple tool call components for demonstration
function WebSearchToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  return (
    <div className="w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px]">
      <div className="text-sm font-medium">Web Search</div>
      <div className="text-sm text-gray-600">
        Query: {(toolCall.args as any)?.query}
      </div>
      {toolCall.result && (
        <div className="text-sm bg-gray-50 p-2 rounded">
          {toolCall.result.slice(0, 200)}...
        </div>
      )}
    </div>
  );
}

function CrawlToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  const url = (toolCall.args as any)?.url;
  return (
    <div className="w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px]">
      <div className="text-sm font-medium flex items-center gap-2">
        📖 Reading
      </div>
      <div className="text-sm text-gray-600">
        URL: {url}
      </div>
    </div>
  );
}

function PythonToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  const code = (toolCall.args as any)?.code;
  return (
    <div className="w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px]">
      <div className="text-sm font-medium flex items-center gap-2">
        🐍 Running Python code
      </div>
      {code && (
        <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
          {code}
        </pre>
      )}
      {toolCall.result && (
        <div className="text-sm bg-gray-50 p-2 rounded">
          Result: {toolCall.result.slice(0, 200)}...
        </div>
      )}
    </div>
  );
}

function RetrieverToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  const keywords = (toolCall.args as any)?.keywords;
  return (
    <div className="w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px]">
      <div className="text-sm font-medium flex items-center gap-2">
        🔍 Retrieving documents
      </div>
      <div className="text-sm text-gray-600">
        Keywords: {keywords}
      </div>
    </div>
  );
}

function GenericToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {
  return (
    <div className="w-full rounded-xl border border-[#E8E9FF] bg-white p-[16px] flex flex-col gap-[16px]">
      <div className="text-sm font-medium flex items-center gap-2">
        🔧 Running {toolCall.name}
      </div>
      {toolCall.result && (
        <div className="text-sm bg-gray-50 p-2 rounded max-h-32 overflow-y-auto">
          {toolCall.result}
        </div>
      )}
    </div>
  );
}

function MessageListItem({
  className,
  message,
  waitForFeedback,
  interruptMessage,
  onFeedback,
  onSendMessage,
}: MessageListItemProps) {
  // Handle step feedback messages
  if (message.stepFeedbackData) {
    return (
      <div className="w-full px-4">
        <StepFeedbackCard
          stepTitle={message.stepFeedbackData.step_title}
          stepDescription={message.stepFeedbackData.step_description}
          stepType={message.stepFeedbackData.step_type}
          executionResult={message.stepFeedbackData.execution_result}
          hasErrors={message.stepFeedbackData.has_errors}
          errorReasons={message.stepFeedbackData.error_reasons}
          errorSummary={message.stepFeedbackData.error_summary}
          onFeedback={(feedback) => {
            onSendMessage?.(feedback, { interruptFeedback: feedback });
          }}
        />
      </div>
    );
  }

  // Handle different agent types
  if (
    message.role === "user" ||
    message.agent === "coordinator" ||
    message.agent === "planner" ||
    message.agent === "podcast" ||
    message.agent === "ppt_generator"
  ) {
    let content: React.ReactNode;
    
    if (message.agent === "planner") {
      content = (
        <div className="w-full px-4">
          <PlanCard
            message={message}
            waitForFeedback={waitForFeedback}
            interruptMessage={interruptMessage}
            onFeedback={onFeedback}
            onSendMessage={onSendMessage}
          />
        </div>
      );
    } else if (message.agent === "podcast") {
      content = (
        <div className="w-full px-4">
          <PodcastCard message={message} />
        </div>
      );
    } else {
      content = message.content ? (
        <div
          className={cn(
            "flex w-full px-4",
            message.role === "user" && "justify-end",
            className,
          )}
        >
          <MessageBubble message={message}>
            <div className="flex w-full flex-col text-wrap break-words">
              <div
                className={cn(
                  "prose prose-sm max-w-none",
                  message.role === "user" &&
                    "prose-invert not-dark:text-secondary dark:text-inherit",
                )}
              >
                {message?.content}
              </div>
            </div>
          </MessageBubble>
        </div>
      ) : null;
    }

    if (content) {
      return (
        <motion.li
          className="mt-10"
          key={message.id}
          initial={{ opacity: 0, y: 24 }}
          animate={{ opacity: 1, y: 0 }}
          style={{ transition: "all 0.2s ease-out" }}
          transition={{
            duration: 0.2,
            ease: "easeOut",
          }}
        >
          {content}
        </motion.li>
      );
    }
  }

  // Handle tool calls
  if (!message.isStreaming && message.toolCalls?.length) {
    return (
      <motion.li
        className="mt-10"
        key={message.id}
        initial={{ opacity: 0, y: 24 }}
        animate={{ opacity: 1, y: 0 }}
        style={{ transition: "all 0.2s ease-out" }}
        transition={{
          duration: 0.2,
          ease: "easeOut",
        }}
      >
        <div className="w-full px-4">
          {message.toolCalls.map((toolCall) => (
            <ToolCallDisplay key={toolCall.id} toolCall={toolCall} />
          ))}
        </div>
      </motion.li>
    );
  }

  return null;
}

// Simple Podcast Card component
function PodcastCard({ message }: { message: Message }) {
  const data = useMemo(() => {
    try {
      return JSON.parse(message.content ?? "{}");
    } catch {
      return {};
    }
  }, [message.content]);
  
  const title = data?.title;
  const audioUrl = data?.audioUrl;
  const isGenerating = message.isStreaming;
  const hasError = data?.error !== undefined;

  return (
    <div className="w-[508px] rounded-xl border bg-white p-4">
      <div className="flex items-center gap-2 mb-3">
        <div className="text-sm text-gray-600">
          {isGenerating ? "🔄 Generating podcast..." : hasError ? "❌ Error" : "🎧 Podcast"}
        </div>
      </div>
      <div className="font-medium text-lg mb-3">
        {title || "Untitled Podcast"}
      </div>
      {audioUrl && (
        <audio
          className="w-full"
          src={audioUrl}
          controls
        />
      )}
      {hasError && (
        <div className="text-red-500 text-sm">
          Error when generating podcast. Please try again.
        </div>
      )}
    </div>
  );
}

// Main component
export function AgentMessageList({
  className,
  messages = [],
  responding = false,
  onFeedback,
  onSendMessage,
}: {
  className?: string;
  messages?: Message[];
  responding?: boolean;
  onFeedback?: (feedback: { option: Option }) => void;
  onSendMessage?: (
    message: string,
    options?: { interruptFeedback?: string },
  ) => void;
}) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  // Find the last interrupt message with options
  const interruptMessage = useMemo(() => {
    return messages.slice().reverse().find(msg => msg.options && msg.options.length > 0) || null;
  }, [messages]);

  // Find the last message waiting for feedback
  const waitingForFeedbackMessage = useMemo(() => {
    return messages.slice().reverse().find(msg => msg.isInterrupt) || null;
  }, [messages]);

  return (
    <div className={cn("flex h-full w-full flex-col overflow-hidden", className)}>
      <div className="flex-1 overflow-y-auto" ref={scrollContainerRef}>
        <ul className="flex flex-col">
          {messages.map((message) => (
            <MessageListItem
              key={message.id}
              message={message}
              waitForFeedback={waitingForFeedbackMessage?.id === message.id}
              interruptMessage={interruptMessage}
              onFeedback={onFeedback}
              onSendMessage={onSendMessage}
            />
          ))}
          <div className="flex h-8 w-full shrink-0"></div>
        </ul>
        {responding && (
          <div className="ml-4 flex items-center space-x-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span className="text-sm text-gray-600">AI is thinking...</span>
          </div>
        )}
      </div>
    </div>
  );
}
