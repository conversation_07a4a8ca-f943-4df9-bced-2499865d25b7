"use client";

import { useMemo } from "react";
import { useAgentStore } from "@/lib/agent/store";
import { cn } from "@/lib/utils";
import { AgentMessagesBlock } from "./AgentMessagesBlock";
import { ResearchBlock } from "./ResearchBlock";

export default function AgentMain() {
  const openResearchId = useAgentStore((state) => state.openResearchId);
  const doubleColumnMode = useMemo(
    () => openResearchId !== null,
    [openResearchId],
  );

  return (
    <div
      className={cn(
        "flex h-full w-full justify-center px-4 pt-4 pb-4",
        doubleColumnMode && "gap-8",
      )}
    >
      <AgentMessagesBlock
        className={cn(
          "shrink-0 transition-all duration-300 ease-out",
          !doubleColumnMode &&
            `w-[768px] translate-x-[min(max(calc((100vw-538px)*0.75),575px)/2,960px/2)]`,
          doubleColumnMode && `w-[538px]`,
        )}
      />
      <ResearchBlock
        className={cn(
          "w-[min(max(calc((100vw-538px)*0.75),575px),960px)] pb-4 transition-all duration-300 ease-out",
          !doubleColumnMode && "scale-0",
          doubleColumnMode && "",
        )}
        researchId={openResearchId}
      />
    </div>
  );
}