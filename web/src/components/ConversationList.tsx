"use client";

import React from "react";
import { MessageSquare, Bot, Plus, MoreHorizontal } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useAppStore } from "@/lib/stores";
import type { ConversationEntry } from "@/lib/types";

interface ConversationListProps {
  currentConversationId?: string;
  onConversationSelect: (id: string) => void;
  onNewConversation: (type?: 'chat' | 'agent') => void;
  className?: string;
}

export const ConversationList: React.FC<ConversationListProps> = ({
  currentConversationId,
  onConversationSelect,
  onNewConversation,
  className
}) => {
  const { conversations, deleteConversation } = useAppStore();

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString('en-US', { weekday: 'short' });
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h2 className="font-semibold text-lg">Conversations</h2>
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={() => onNewConversation('chat')}
              className="h-8 w-8 p-0"
              title="New Chat"
            >
              <MessageSquare className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onNewConversation('agent')}
              className="h-8 w-8 p-0"
              title="New Agent Session"
            >
              <Bot className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Conversation List */}
      <div className="flex-1 overflow-y-auto">
        {conversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <div className="mb-4">
              <MessageSquare className="w-12 h-12 mx-auto text-gray-300" />
            </div>
            <p className="text-sm mb-4">No conversations yet</p>
            <div className="space-y-2">
              <Button
                onClick={() => onNewConversation('chat')}
                className="w-full"
                size="sm"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Start Chat
              </Button>
              <Button
                onClick={() => onNewConversation('agent')}
                variant="outline"
                className="w-full"
                size="sm"
              >
                <Bot className="w-4 h-4 mr-2" />
                Start Agent Research
              </Button>
            </div>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {conversations.map((conversation) => (
              <div
                key={conversation.id}
                className={cn(
                  "group flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors",
                  "hover:bg-gray-100 dark:hover:bg-gray-800",
                  currentConversationId === conversation.id 
                    ? "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800"
                    : "border border-transparent"
                )}
                onClick={() => onConversationSelect(conversation.id)}
              >
                {/* Icon */}
                <div className={cn(
                  "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
                  conversation.type === 'chat' 
                    ? "bg-blue-500 text-white" 
                    : "bg-green-500 text-white"
                )}>
                  {conversation.type === 'chat' ? (
                    <MessageSquare className="w-4 h-4" />
                  ) : (
                    <Bot className="w-4 h-4" />
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">
                      {conversation.title || `${conversation.type === 'chat' ? 'Chat' : 'Agent Research'} Session`}
                    </h3>
                    <span className="text-xs text-gray-500 flex-shrink-0">
                      {formatTimestamp(conversation.timestamp)}
                    </span>
                  </div>
                  {conversation.lastMessage && (
                    <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                      {truncateText(conversation.lastMessage, 60)}
                    </p>
                  )}
                </div>

                {/* Actions */}
                <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (confirm('Are you sure you want to delete this conversation?')) {
                        deleteConversation(conversation.id);
                      }
                    }}
                  >
                    <MoreHorizontal className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};