'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuthStore, useChatStore } from '@/lib/stores';
import { useChats } from '@/hooks/useChats';

import { Messages } from './Messages';
import { MessageInput } from './MessageInput';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { generateOpenAIChatCompletion, createOpenAITextStream } from '@/lib/api/chat-completion';
import type { Message } from '@/lib/types';

interface ChatProps {
  chatId?: string;
  selectedModels?: string[];
  initialMessages?: Message[];
  onUpdate?: (updates: any) => void;
  className?: string;
}

export const Chat: React.FC<ChatProps> = ({
  chatId,
  selectedModels = [],
  initialMessages = [],
  onUpdate,
  className
}) => {
  const { token } = useAuthStore();
  const { currentChat } = useChatStore();

  const { loadChat, createChat, updateChatData } = useChats();

  const [prompt, setPrompt] = useState('');
  const [files, setFiles] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [autoScroll] = useState(true);

  // Load chat when chatId changes
  useEffect(() => {
    if (chatId && chatId !== 'new') {
      // Only try to load from server if we're in a server chat route (starts with actual server ID pattern)
      // Skip loading for local/temporary chats
      if (window.location.pathname.startsWith('/c/')) {
        loadChat(chatId);
      } else {
        // This is likely a local chat, don't try to load from server
        console.log('Local chat detected, skipping server load:', chatId);
      }
    }
  }, [chatId, loadChat]);

  // Update messages when current chat changes
  useEffect(() => {
    if (currentChat?.messages) {
      setMessages(currentChat.messages);
    } else {
      setMessages([]);
    }
  }, [currentChat]);

  // Using HTTP API for all chat functionality

  // Handle message submission
  const handleSubmit = useCallback(async (data: {
    prompt: string;
    files?: any[];
    selectedModels: string[];
  }) => {
    if (!data.prompt.trim() && (!data.files || data.files.length === 0)) {
      toast.error('Please enter a prompt');
      return;
    }

    // Check if models are selected and not empty
    if (!data.selectedModels.length || data.selectedModels.includes('')) {
      toast.error('Model not selected');
      return;
    }

    if (!token) {
      toast.error('Please sign in to continue');
      return;
    }

    setIsLoading(true);

    try {
      // Create user message
      const userMessage: Message = {
        id: Math.random().toString(36).substring(2, 11),
        role: 'user',
        content: data.prompt,
        timestamp: Math.floor(Date.now() / 1000),
        files: data.files || []
      };

      // Add user message to local state immediately
      const newMessages = [...messages, userMessage];
      setMessages(newMessages);

      // Generate AI response with streaming first
      await generateAIResponse(userMessage, data.selectedModels, newMessages);

      // Create or update chat after starting AI response
      let currentChatId = chatId;
      if (!currentChatId || currentChatId === 'new') {
        const newChat = await createChat({
          title: data.prompt.slice(0, 50) + (data.prompt.length > 50 ? '...' : ''),
          messages: [userMessage]
        });

        if (newChat?.id) {
          currentChatId = newChat.id;
          // Replace current URL without causing navigation/re-render
          // This matches the original Svelte behavior using window.history.replaceState
          window.history.replaceState(window.history.state, '', `/c/${currentChatId}`);
        } else {
          console.warn('Failed to create chat, continuing with temporary chat');
        }
      } else {
        // Update existing chat with new message
        await updateChatData(currentChatId, {
          messages: newMessages,
          updated_at: Math.floor(Date.now() / 1000)
        });
      }

      // Clear input
      setPrompt('');
      setFiles([]);

    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
      setIsLoading(false);
    }
  }, [messages, chatId, token, createChat, updateChatData]);

  // Generate AI response with streaming
  const generateAIResponse = useCallback(async (
    userMessage: Message,
    selectedModels: string[],
    currentMessages: Message[]
  ) => {
    try {
      // Process each selected model
      for (const modelId of selectedModels) {
        const responseMessageId = Math.random().toString(36).substring(2, 11);

        // Create assistant message placeholder
        const assistantMessage: Message = {
          id: responseMessageId,
          role: 'assistant',
          content: '',
          timestamp: Math.floor(Date.now() / 1000),
          model: modelId,
          parentId: userMessage.id,
          childrenIds: []
        };

        // Add assistant message to state
        setMessages(prev => [...prev, assistantMessage]);

        // Use HTTP API directly (WebSocket functionality disabled for now)
        await generateHTTPResponse(userMessage, modelId, responseMessageId, currentMessages);
      }
    } catch (error) {
      console.error('Failed to generate AI response:', error);
      toast.error('Failed to generate response');
      setIsLoading(false);
    }
  }, [token, setMessages]);

  // Generate response via HTTP API
  const generateHTTPResponse = useCallback(async (
    userMessage: Message,
    modelId: string,
    responseMessageId: string,
    currentMessages: Message[]
  ) => {
    try {
      // Prepare messages for API (OpenAI format), filtering out tool messages
      const apiMessages = currentMessages
        .filter(msg => msg.role !== 'tool')
        .map(msg => ({
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content
        }));

      // Add current user message
      apiMessages.push({
        role: userMessage.role as 'user' | 'assistant' | 'system',
        content: userMessage.content
      });

      const response = await generateOpenAIChatCompletion(token!, {
        model: modelId,
        messages: apiMessages,
        stream: true,
        temperature: 0.7,
        max_tokens: 2000
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Process streaming response using createOpenAITextStream
      const textStream = createOpenAITextStream(response.body!, true);

      for await (const update of textStream) {
        if (!update.done && update.value) {
          // Update message content with delta
          setMessages(prev => prev.map(msg =>
            msg.id === responseMessageId
              ? { ...msg, content: msg.content + update.value }
              : msg
          ));
        }

        if (update.done) {
          // Mark message as complete
          setMessages(prev => prev.map(msg =>
            msg.id === responseMessageId
              ? { ...msg, done: true }
              : msg
          ));
          break;
        }

        // Handle any errors in the stream
        // Note: Our TextStreamUpdate doesn't have error property,
        // errors are handled in the catch block
      }

    } catch (error) {
      console.error('HTTP API error for model', modelId, ':', error);

      // Update message with error
      const errorMessage = error instanceof Error ? error.message : String(error);
      setMessages(prev => prev.map(msg =>
        msg.id === responseMessageId
          ? { ...msg, content: `Error: ${errorMessage}`, error: true }
          : msg
      ));
    }
  }, [token, setMessages]);



  // WebSocket streaming removed - using HTTP streaming only

  // Handle prompt change
  const handlePromptChange = useCallback((newPrompt: string) => {
    setPrompt(newPrompt);
  }, []);



  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Messages area */}
      <div className="flex-1 overflow-hidden">
        <Messages
          chatId={chatId}
          chat={currentChat}
          messages={messages}
          selectedModels={selectedModels}
          prompt={prompt}
          onPromptChange={handlePromptChange}
          onSubmit={handleSubmit}
          autoScroll={autoScroll}
          bottomPadding={true}
          temporaryChatEnabled={false}
        />
      </div>

      {/* Message input area */}
      {messages.length > 0 && (
        <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
          <div className="max-w-4xl mx-auto p-4">
            <MessageInput
              prompt={prompt}
              onPromptChange={handlePromptChange}
              onSubmit={handleSubmit}
              disabled={isLoading}
              selectedModels={selectedModels}
              files={files}
              onFilesChange={setFiles}
              placeholder="Type your message..."
            />
            
            {/* Loading indicator */}
            {isLoading && (
              <div className="flex items-center justify-center mt-2 text-sm text-gray-500">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 dark:border-white mr-2"></div>
                Generating response...
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
