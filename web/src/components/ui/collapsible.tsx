'use client';

import React, { useState, useRef, useEffect } from 'react';
import * as CollapsiblePrimitive from "@radix-ui/react-collapsible";
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronRight } from 'lucide-react';

// Export Radix UI Collapsible components for compatibility
export const Collapsible = CollapsiblePrimitive.Root;
export const CollapsibleTrigger = CollapsiblePrimitive.Trigger;
export const CollapsibleContent = CollapsiblePrimitive.Content;

interface CustomCollapsibleProps {
  children: React.ReactNode;
  trigger: React.ReactNode;
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  className?: string;
  triggerClassName?: string;
  contentClassName?: string;
  disabled?: boolean;
  animationDuration?: number;
}

export const CustomCollapsible: React.FC<CustomCollapsibleProps> = ({
  children,
  trigger,
  defaultOpen = false,
  open: controlledOpen,
  onOpenChange,
  className,
  triggerClassName,
  contentClassName,
  disabled = false,
  animationDuration = 200
}) => {
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  const contentRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState<number | undefined>(defaultOpen ? undefined : 0);
  
  const isControlled = controlledOpen !== undefined;
  const isOpen = isControlled ? controlledOpen : internalOpen;

  const handleToggle = () => {
    if (disabled) return;
    
    const newOpen = !isOpen;
    
    if (isControlled) {
      onOpenChange?.(newOpen);
    } else {
      setInternalOpen(newOpen);
    }
  };

  useEffect(() => {
    if (!contentRef.current) return;

    if (isOpen) {
      const contentHeight = contentRef.current.scrollHeight;
      setHeight(contentHeight);
    } else {
      setHeight(0);
    }
  }, [isOpen]);

  return (
    <div className={cn('w-full', className)}>
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className={cn(
          'flex items-center justify-between w-full text-left',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded',
          disabled && 'opacity-50 cursor-not-allowed',
          triggerClassName
        )}
        aria-expanded={isOpen}
      >
        <span className="flex-1">{trigger}</span>
        <ChevronDown
          className={cn(
            'w-4 h-4 transition-transform duration-200',
            isOpen ? 'transform rotate-180' : ''
          )}
        />
      </button>
      
      <div
        ref={contentRef}
        style={{
          height: height,
          transition: `height ${animationDuration}ms ease-in-out`
        }}
        className="overflow-hidden"
      >
        <div className={cn('pt-2', contentClassName)}>
          {children}
        </div>
      </div>
    </div>
  );
};

// Simple Collapsible with just chevron icon
interface SimpleCollapsibleProps {
  children: React.ReactNode;
  title: string;
  defaultOpen?: boolean;
  className?: string;
  icon?: React.ReactNode;
}

export const SimpleCollapsible: React.FC<SimpleCollapsibleProps> = ({
  children,
  title,
  defaultOpen = false,
  className,
  icon
}) => {
  return (
    <CustomCollapsible
      defaultOpen={defaultOpen}
      trigger={
        <div className="flex items-center gap-2 py-2">
          {icon}
          <span className="font-medium">{title}</span>
        </div>
      }
      className={className}
      triggerClassName="hover:bg-gray-50 dark:hover:bg-gray-800 px-3 py-1 rounded-lg transition-colors"
      contentClassName="px-3"
    >
      {children}
    </CustomCollapsible>
  );
};

// Accordion component using multiple collapsibles
interface AccordionItem {
  id: string;
  title: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface AccordionProps {
  items: AccordionItem[];
  type?: 'single' | 'multiple';
  defaultValue?: string | string[];
  value?: string | string[];
  onValueChange?: (value: string | string[]) => void;
  className?: string;
  collapsible?: boolean;
}

export const Accordion: React.FC<AccordionProps> = ({
  items,
  type = 'single',
  defaultValue,
  value: controlledValue,
  onValueChange,
  className,
  collapsible = true
}) => {
  const [internalValue, setInternalValue] = useState<string | string[]>(
    defaultValue || (type === 'single' ? '' : [])
  );

  const isControlled = controlledValue !== undefined;
  const currentValue = isControlled ? controlledValue : internalValue;

  const handleValueChange = (itemId: string) => {
    let newValue: string | string[];

    if (type === 'single') {
      const currentSingle = currentValue as string;
      newValue = currentSingle === itemId && collapsible ? '' : itemId;
    } else {
      const currentMultiple = currentValue as string[];
      newValue = currentMultiple.includes(itemId)
        ? currentMultiple.filter(id => id !== itemId)
        : [...currentMultiple, itemId];
    }

    if (isControlled) {
      onValueChange?.(newValue);
    } else {
      setInternalValue(newValue);
    }
  };

  const isItemOpen = (itemId: string) => {
    if (type === 'single') {
      return currentValue === itemId;
    } else {
      return (currentValue as string[]).includes(itemId);
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      {items.map((item) => (
        <div
          key={item.id}
          className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
        >
          <CustomCollapsible
            open={isItemOpen(item.id)}
            onOpenChange={() => handleValueChange(item.id)}
            disabled={item.disabled}
            trigger={
              <div className="flex items-center gap-2 p-4">
                {item.icon}
                <span className="font-medium">{item.title}</span>
              </div>
            }
            triggerClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors w-full"
            contentClassName="px-4 pb-4"
          >
            {item.content}
          </CustomCollapsible>
        </div>
      ))}
    </div>
  );
};

// Details/Summary style collapsible
interface DetailsProps {
  children: React.ReactNode;
  summary: React.ReactNode;
  defaultOpen?: boolean;
  className?: string;
}

export const Details: React.FC<DetailsProps> = ({
  children,
  summary,
  defaultOpen = false,
  className
}) => {
  return (
    <details className={cn('group', className)} open={defaultOpen}>
      <summary className="flex items-center justify-between cursor-pointer list-none p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors">
        <span>{summary}</span>
        <ChevronRight className="w-4 h-4 transition-transform group-open:rotate-90" />
      </summary>
      <div className="mt-2 pl-4">
        {children}
      </div>
    </details>
  );
};

// Expandable text component
interface ExpandableTextProps {
  children: string;
  maxLength?: number;
  className?: string;
  expandText?: string;
  collapseText?: string;
}

export const ExpandableText: React.FC<ExpandableTextProps> = ({
  children,
  maxLength = 150,
  className,
  expandText = 'Show more',
  collapseText = 'Show less'
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  if (children.length <= maxLength) {
    return <span className={className}>{children}</span>;
  }

  const truncatedText = children.slice(0, maxLength) + '...';

  return (
    <span className={className}>
      {isExpanded ? children : truncatedText}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="ml-2 text-blue-600 dark:text-blue-400 hover:underline focus:outline-none"
      >
        {isExpanded ? collapseText : expandText}
      </button>
    </span>
  );
};
