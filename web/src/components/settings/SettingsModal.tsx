'use client';

import React, { useState } from 'react';
import { Modal } from '@/components/common';
import { AccountSettings } from './AccountSettings';
import { GeneralSettings } from './GeneralSettings';
import { InterfaceSettings } from './InterfaceSettings';
import {
  AudioSettings,
  ChatsSettings,
  PersonalizationSettings,
  ConnectionsSettings,
  ToolsSettings,
  AboutSettings
} from './index';
import { cn } from '@/lib/utils';
import { 
  UserIcon, 
  SettingsIcon, 
  EyeIcon, 
  ChatBubbleIcon,
  DocumentIcon 
} from '@/components/icons';

type SettingsTab = 
  | 'general' 
  | 'interface' 
  | 'audio' 
  | 'chats' 
  | 'account' 
  | 'personalization'
  | 'connections'
  | 'tools'
  | 'about';

interface SettingsModalProps {
  show: boolean;
  onClose: () => void;
  selectedTab?: SettingsTab;
}

interface TabConfig {
  id: SettingsTab;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  component: React.ComponentType<any>;
}



export const SettingsModal: React.FC<SettingsModalProps> = ({
  show,
  onClose,
  selectedTab = 'general'
}) => {
  const [activeTab, setActiveTab] = useState<SettingsTab>(selectedTab);

  const tabs: TabConfig[] = [
    {
      id: 'general',
      label: 'General',
      icon: SettingsIcon,
      component: GeneralSettings
    },
    {
      id: 'interface',
      label: 'Interface',
      icon: EyeIcon,
      component: InterfaceSettings
    },
    {
      id: 'audio',
      label: 'Audio',
      icon: ({ className }) => (
        <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
          <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
          <line x1="12" y1="19" x2="12" y2="23"/>
          <line x1="8" y1="23" x2="16" y2="23"/>
        </svg>
      ),
      component: AudioSettings
    },
    {
      id: 'chats',
      label: 'Chats',
      icon: ChatBubbleIcon,
      component: ChatsSettings
    },
    {
      id: 'account',
      label: 'Account',
      icon: UserIcon,
      component: AccountSettings
    },
    {
      id: 'personalization',
      label: 'Personalization',
      icon: ({ className }) => (
        <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
          <circle cx="12" cy="7" r="4"/>
        </svg>
      ),
      component: PersonalizationSettings
    },
    {
      id: 'connections',
      label: 'Connections',
      icon: ({ className }) => (
        <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
          <rect x="2" y="9" width="4" height="12"/>
          <circle cx="4" cy="4" r="2"/>
        </svg>
      ),
      component: ConnectionsSettings
    },
    {
      id: 'tools',
      label: 'Tools',
      icon: ({ className }) => (
        <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
        </svg>
      ),
      component: ToolsSettings
    },
    {
      id: 'about',
      label: 'About',
      icon: DocumentIcon,
      component: AboutSettings
    }
  ];

  const activeTabConfig = tabs.find(tab => tab.id === activeTab);
  const ActiveComponent = activeTabConfig?.component;

  const handleSaveSettings = async (settings: any) => {
    // Implementation for saving settings
    console.log('Saving settings:', settings);
  };

  return (
    <Modal
      show={show}
      onClose={onClose}
      size="lg"
      className="bg-white dark:bg-gray-900"
    >
      <div className="flex flex-col h-[80vh] max-h-[800px]">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Settings
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <div className="w-64 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="p-4">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={cn(
                        "w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                        activeTab === tab.id
                          ? "bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200"
                          : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white"
                      )}
                    >
                      <Icon className="w-5 h-5 mr-3" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              {ActiveComponent && (
                <ActiveComponent
                  saveSettings={handleSaveSettings}
                  onSave={() => {
                    // Handle save success
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};
