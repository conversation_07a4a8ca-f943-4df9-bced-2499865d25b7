import { WEBUI_API_BASE_URL } from '@/lib/constants';
import type { Message } from '@/lib/types';

export interface ChatCompletionMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatCompletionMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
}

export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Generate OpenAI-compatible chat completion
export const generateOpenAIChatCompletion = async (
  token: string,
  body: ChatCompletionRequest,
  baseUrl?: string
): Promise<Response> => {
  // Use the correct Open WebUI endpoint - use /api path without /v1
  const apiBaseUrl = baseUrl || WEBUI_API_BASE_URL.replace('/api/v1', '');
  const url = `${apiBaseUrl}/api/chat/completions`;
  
  console.log('Sending chat completion request:', { url, body });
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Chat completion failed: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
    }

    return response;
  } catch (error) {
    console.error('Chat completion API error:', error);
    throw error;
  }
};

// Stream processing utilities
export interface TextStreamUpdate {
  done: boolean;
  value: string;
}

// Process streaming response from OpenAI format
export async function* createOpenAITextStream(
  responseBody: ReadableStream<Uint8Array>,
  splitLargeDeltas: boolean = false
): AsyncGenerator<TextStreamUpdate> {
  const reader = responseBody.getReader();
  const decoder = new TextDecoder();
  
  try {
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n');

      for (const line of lines) {
        const trimmedLine = line.trim();
        
        if (!trimmedLine || !trimmedLine.startsWith('data: ')) {
          continue;
        }

        const data = trimmedLine.slice(6); // Remove 'data: ' prefix
        
        if (data === '[DONE]') {
          return;
        }

        try {
          const parsed = JSON.parse(data);
          
          if (parsed.choices && parsed.choices[0]?.delta?.content) {
            const content = parsed.choices[0].delta.content;
            
            if (splitLargeDeltas && content.length > 5) {
              // Split large deltas for smoother streaming effect
              for (let i = 0; i < content.length; i += Math.floor(Math.random() * 3) + 1) {
                const chunk = content.slice(i, i + Math.floor(Math.random() * 3) + 1);
                yield { done: false, value: chunk };
                // Small delay for typing effect
                await new Promise(resolve => setTimeout(resolve, 10));
              }
            } else {
              yield { done: false, value: content };
            }
          }
        } catch (parseError) {
          console.warn('Failed to parse SSE data:', data, parseError);
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}

// Send prompt via Socket.IO to backend
export const sendPromptSocket = async (
  socket: any,
  data: {
    chatId: string;
    messageId: string;
    message: Message;
    models: string[];
    files?: any[];
  }
) => {
  if (!socket?.connected) {
    throw new Error('WebSocket not connected');
  }

  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('Socket timeout'));
    }, 30000); // 30 second timeout

    socket.emit('chat', {
      chat_id: data.chatId,
      message_id: data.messageId,
      message: data.message,
      models: data.models,
      files: data.files || []
    }, (response: any) => {
      clearTimeout(timeout);
      
      if (response?.error) {
        reject(new Error(response.error));
      } else {
        resolve(response);
      }
    });
  });
};

export default {
  generateOpenAIChatCompletion,
  createOpenAITextStream,
  sendPromptSocket
};