import { WEBUI_API_BASE_URL } from '@/lib/constants';
import type { SessionUser, User } from '@/lib/types';

// Admin APIs
export const getAdminDetails = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/admin/details`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get admin details:', error);
    throw error;
  }
};

export const getAdminConfig = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/admin/config`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get admin config:', error);
    throw error;
  }
};

export const updateAdminConfig = async (token: string, body: object): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/admin/config`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update admin config:', error);
    throw error;
  }
};

// Session APIs
export const getSessionUser = async (token: string): Promise<SessionUser> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      credentials: 'include'
    });

    if (!response.ok) {
      const contentType = response.headers.get('content-type');
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      
      try {
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || errorMessage;
        } else {
          const text = await response.text();
          if (text.includes('<!doctype') || text.includes('<html')) {
            errorMessage = `Session API returned HTML instead of JSON. Status: ${response.status}. The API endpoint may not exist.`;
          } else {
            errorMessage = `Session API returned non-JSON response: ${text.substring(0, 200)}...`;
          }
        }
      } catch (parseError) {
        errorMessage = `Failed to parse session error response. Status: ${response.status}`;
      }
      
      console.error('Session API error:', errorMessage);
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get('content-type');
    const responseText = await response.text();
    
    // Check if response is empty
    if (!responseText || responseText.trim() === '') {
      console.warn('Session API returned empty response, treating as API not available');
      throw new Error('Session API returned empty response');
    }
    
    // Check if response is JSON
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error(`Session API returned non-JSON response: ${responseText.substring(0, 200)}...`);
    }

    try {
      return JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse session response as JSON:', responseText.substring(0, 200));
      throw new Error(`Session API returned invalid JSON: ${responseText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.error('Failed to get session user:', error);
    throw error;
  }
};

// User sign in
export const userSignIn = async (email: string, password: string): Promise<{ token: string; user: SessionUser }> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to sign in:', error);
    throw error;
  }
};

// User sign up
export const userSignUp = async (
  name: string,
  email: string,
  password: string,
  profile_image_url?: string
): Promise<{ token: string; user: SessionUser }> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name,
        email,
        password,
        profile_image_url
      })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to sign up:', error);
    throw error;
  }
};

// User sign out
export const userSignOut = async (): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/signout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to sign out:', error);
    throw error;
  }
};

// LDAP sign in
export const ldapUserSignIn = async (email: string, password: string): Promise<{ token: string; user: SessionUser }> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/ldap/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to LDAP sign in:', error);
    throw error;
  }
};

// Update user profile
export const updateUserProfile = async (
  token: string,
  name: string,
  profile_image_url?: string
): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/update/profile`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify({
        name,
        profile_image_url
      })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update user profile:', error);
    throw error;
  }
};

// Update user password
export const updateUserPassword = async (
  token: string,
  currentPassword: string,
  newPassword: string
): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/update/password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify({
        password: currentPassword,
        new_password: newPassword
      })
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to update user password:', error);
    throw error;
  }
};

// Get API keys
export const getAPIKeys = async (token: string): Promise<any[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/api_keys`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get API keys:', error);
    throw error;
  }
};

// Create API key
export const createAPIKey = async (token: string, comment?: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/api_keys`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ comment })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to create API key:', error);
    throw error;
  }
};

// Delete API key
export const deleteAPIKey = async (token: string, keyId: string): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/auths/api_keys/${keyId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete API key:', error);
    throw error;
  }
};

// Get API key (get the first available key)
export const getAPIKey = async (token: string): Promise<string | null> => {
  try {
    const keys = await getAPIKeys(token);
    return keys.length > 0 ? keys[0].api_key : null;
  } catch (error) {
    console.error('Failed to get API key:', error);
    return null;
  }
};

// Get Gravatar URL
export const getGravatarUrl = async (token: string, email: string): Promise<string> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/utils/gravatar?email=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const result = await response.json();
    return result.url || `https://www.gravatar.com/avatar/${btoa(email.toLowerCase())}?d=identicon`;
  } catch (error) {
    console.error('Failed to get Gravatar URL:', error);
    // Fallback to default Gravatar URL
    return `https://www.gravatar.com/avatar/${btoa(email.toLowerCase())}?d=identicon`;
  }
};
