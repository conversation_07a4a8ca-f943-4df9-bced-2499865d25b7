import { WEBUI_API_BASE_URL } from '@/lib/constants';
import type { Chat, Message, PaginatedResponse } from '@/lib/types';

// Create new chat
export const createNewChat = async (token: string, chat: object): Promise<Chat> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/new`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ chat })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to create new chat:', error);
    throw error;
  }
};

// Import chat
export const importChat = async (
  token: string,
  chat: object,
  meta: object | null,
  pinned?: boolean,
  folderId?: string | null
): Promise<Chat> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/import`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({
        chat,
        meta: meta ?? {},
        pinned,
        folder_id: folderId
      })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to import chat:', error);
    throw error;
  }
};

// Get chat list
export const getChatList = async (
  token: string = '',
  page: number | null = null
): Promise<PaginatedResponse<Chat>> => {
  try {
    const searchParams = new URLSearchParams();
    if (page !== null) {
      searchParams.append('page', `${page}`);
    }

    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/?${searchParams.toString()}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...(token && { authorization: `Bearer ${token}` })
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const chats = await response.json();

    // Backend returns array directly, wrap it in expected format
    return {
      data: chats,
      total: chats.length,
      page: page || 1,
      limit: 60,
      has_more: false // Since we don't have pagination info from backend
    };
  } catch (error) {
    console.error('Failed to get chat list:', error);
    throw error;
  }
};

// Get chat by ID
export const getChatById = async (token: string, chatId: string): Promise<Chat> => {
  if (!chatId || chatId.trim() === '') {
    throw new Error('Chat ID is required');
  }

  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get chat by ID:', chatId, error);
    throw error;
  }
};

// Update chat by ID
export const updateChatById = async (
  token: string,
  chatId: string,
  chat: Partial<Chat>
): Promise<Chat> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ chat })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update chat:', error);
    throw error;
  }
};

// Delete chat by ID
export const deleteChatById = async (token: string, chatId: string): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete chat:', error);
    throw error;
  }
};

// Get chat by share ID
export const getChatByShareId = async (shareId: string): Promise<Chat> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/share/${shareId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get chat by share ID:', error);
    throw error;
  }
};

// Clone shared chat by ID
export const cloneSharedChatById = async (
  token: string,
  shareId: string
): Promise<Chat> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/share/${shareId}/clone`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to clone shared chat:', error);
    throw error;
  }
};

// Get all tags
export const getAllTags = async (token: string): Promise<string[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/tags`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const result = await response.json();
    return result.tags || [];
  } catch (error) {
    console.error('Failed to get all tags:', error);
    throw error;
  }
};

// Get tags by chat ID
export const getTagsById = async (token: string, chatId: string): Promise<string[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/tags`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const result = await response.json();
    return result.tags || [];
  } catch (error) {
    console.error('Failed to get tags by ID:', error);
    throw error;
  }
};

// Add tag by ID
export const addTagById = async (
  token: string,
  chatId: string,
  tagName: string
): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/tags`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ tag_name: tagName })
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to add tag:', error);
    throw error;
  }
};

// Delete tag by ID
export const deleteTagById = async (
  token: string,
  chatId: string,
  tagName: string
): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/tags`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({ tag_name: tagName })
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete tag:', error);
    throw error;
  }
};

// Delete tags by ID
export const deleteTagsById = async (
  token: string,
  chatId: string
): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/chats/${chatId}/tags/all`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete all tags:', error);
    throw error;
  }
};
