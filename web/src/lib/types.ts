// Unified types for the application

// Base message interface
export interface BaseMessage {
  id: string;
  role: 'user' | 'assistant' | 'tool';
  content: string;
  timestamp?: number;
  files?: any[];
}

// Chat message (existing)
export interface Message extends BaseMessage {
  done?: boolean;
  model?: string;
}

// Agent message (imported from agent types)
export interface AgentMessage extends BaseMessage {
  threadId: string;
  agent?:
    | "coordinator"
    | "planner"
    | "researcher"
    | "coder"
    | "reporter"
    | "podcast"
    | "ppt_generator";
  isStreaming?: boolean;
  contentChunks: string[];
  reasoningContent?: string;
  reasoningContentChunks?: string[];
  toolCalls?: ToolCallRuntime[];
  options?: Option[];
  finishReason?: "stop" | "interrupt" | "tool_calls";
  interruptFeedback?: string;
  resources?: Array<Resource>;
  isInterrupt?: boolean;
  interruptType?: "human_feedback" | "step_feedback";
  stepFeedbackData?: {
    step_title: string;
    step_description: string;
    step_type: "research" | "processing";
    execution_result: string;
    has_errors?: boolean;
    error_reasons?: string[];
    error_summary?: string;
  };
}

// Unified conversation entry that can hold both chat and agent messages
export interface ConversationEntry {
  id: string;
  type: 'chat' | 'agent';
  timestamp: number;
  title?: string;
  lastMessage?: string;
  // For chat conversations
  chatData?: {
    messages: Message[];
    selectedModels: string[];
  };
  // For agent conversations  
  agentData?: {
    messages: AgentMessage[];
    threadId: string;
    researchIds: string[];
  };
}

// Chat-related types
export interface Chat {
  id: string;
  title?: string;
  messages: Message[];
  created_at?: number;
  updated_at?: number;
}

// User and config types
export interface User {
  id: string;
  name: string;
  email: string;
  profile_image_url?: string;
  role?: string;
  permissions?: any;
}

export interface SessionUser extends User {
  token?: string;
}

export interface Config {
  enable_signup?: boolean;
  features?: {
    enable_websocket?: boolean;
  };
}

export interface Model {
  id: string;
  name: string;
  owned_by?: string;
  info?: {
    meta?: {
      description?: string;
      capabilities?: string[];
      provider?: string;
      pricing?: {
        input?: number;
        output?: number;
      };
      context_length?: number;
      vision?: boolean;
      function_calling?: boolean;
      toolIds?: string[];
    };
  };
  pipe?: {
    type?: string;
  };
}

// Settings types
export interface UserSettings {
  theme: 'system' | 'light' | 'dark';
  language: string;
  fontSize: string;
  codeTheme: string;
  showTimestamp: boolean;
  showUsername: boolean;
  enableNotifications: boolean;
  enableSounds: boolean;
}

export type ThemeMode = 'system' | 'light' | 'dark';

// UI types
export interface Banner {
  id: string;
  type: 'info' | 'warning' | 'error';
  title: string;
  content: string;
  dismissible?: boolean;
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

// Agent-specific types (from agent module)
export interface Option {
  text: string;
  value: string;
  content?: string;
}

export interface ToolCallRuntime {
  id: string;
  name: string;
  args: Record<string, unknown>;
  argsChunks?: string[];
  result?: string;
}

export interface Resource {
  uri: string;
  title: string;
}

// API response types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}