// Agent Message Types - Complete implementation from librai-agent-flow
export type MessageRole = "user" | "assistant" | "tool";

export interface Message {
  id: string;
  threadId: string;
  agent?:
    | "coordinator"
    | "planner"
    | "researcher"
    | "coder"
    | "reporter"
    | "podcast"
    | "ppt_generator";
  role: MessageRole;
  isStreaming?: boolean;
  content: string;
  contentChunks: string[];
  reasoningContent?: string;
  reasoningContentChunks?: string[];
  toolCalls?: ToolCallRuntime[];
  options?: Option[];
  finishReason?: "stop" | "interrupt" | "tool_calls";
  interruptFeedback?: string;
  resources?: Array<Resource>;
  isInterrupt?: boolean;
  interruptType?: "human_feedback" | "step_feedback";
  stepFeedbackData?: {
    step_title: string;
    step_description: string;
    step_type: "research" | "processing";
    execution_result: string;
    has_errors?: boolean;
    error_reasons?: string[];
    error_summary?: string;
  };
}

export interface Option {
  text: string;
  value: string;
  content?: string;
}

export interface ToolCallRuntime {
  id: string;
  name: string;
  args: Record<string, unknown>;
  argsChunks?: string[];
  result?: string;
}

export interface Resource {
  uri: string;
  title: string;
}

// Tool call specific types
export interface WeatherData {
  location: string;
  temperature: number;
  condition: string;
  forecast?: Array<{
    date: string;
    high: number;
    low: number;
    condition: string;
  }>;
}

export interface HotelData {
  name: string;
  rating: number;
  price: string;
  image?: string;
  url?: string;
  location?: string;
  description?: string;
}

export interface EventData {
  title: string;
  date: string;
  time: string;
  location: string;
  description?: string;
  url?: string;
}

export interface TimezoneData {
  from_timezone: string;
  to_timezone: string;
  from_time: string;
  to_time: string;
  date: string;
}

// Agent workflow types
export interface AgentPlan {
  title: string;
  description: string;
  steps: Array<{
    title: string;
    description: string;
    agent: string;
  }>;
}

export interface ResearchData {
  id: string;
  title: string;
  status: "planning" | "researching" | "coding" | "reporting" | "completed";
  planMessage?: Message;
  activities: Message[];
  report?: Message;
}

// Settings types
export interface AgentSettings {
  autoAcceptedPlan: boolean;
  enableDeepThinking: boolean;
  enableBackgroundInvestigation: boolean;
  maxPlanIterations: number;
  maxStepNum: number;
  maxSearchResults: number;
  reportStyle: "brief" | "detailed" | "comprehensive";
  mcpSettings?: any;
}

// Event types for WebSocket communication
export interface AgentStreamEvent {
  type: "content" | "reasoning" | "tool_call" | "tool_call_result" | "interrupt" | "step_feedback" | "finish";
  data: any;
}

export interface StepFeedbackEvent {
  type: "step_feedback";
  data: {
    id: string;
    thread_id: string;
    agent: string;
    role: MessageRole;
    step_title: string;
    step_description: string;
    step_type: "research" | "processing";
    execution_result: string;
    has_errors?: boolean;
    error_reasons?: string[];
    error_summary?: string;
  };
}

// File upload types
export interface AgentFile {
  id: string;
  name: string;
  type: string;
  size: number;
  content?: string;
  url?: string;
}