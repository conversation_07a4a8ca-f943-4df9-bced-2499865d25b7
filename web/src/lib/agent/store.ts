// Complete Agent Store Implementation from librai-agent-flow
import { nanoid } from "nanoid";
import { toast } from "sonner";
import { create } from "zustand";
import { useShallow } from "zustand/react/shallow";
import { getWebSocketClient } from "@/lib/websocket/client";

// Types
export interface ToolCallRuntime {
  id: string;
  name: string;
  args: Record<string, unknown>;
  argsChunks?: string[];
  result?: string;
}

export interface Option {
  text: string;
  value: string;
  content?: string;
}

export interface Resource {
  uri: string;
  title: string;
}

export interface Message {
  id: string;
  threadId: string;
  role: "user" | "assistant" | "tool";
  agent?: "coordinator" | "planner" | "researcher" | "coder" | "reporter" | "podcast" | "ppt_generator";
  content: string;
  contentChunks: string[];
  reasoningContent?: string;
  reasoningContentChunks?: string[];
  toolCalls?: ToolCallRuntime[];
  options?: Option[];
  finishReason?: "stop" | "interrupt" | "tool_calls";
  interruptFeedback?: string;
  resources?: Array<Resource>;
  isStreaming?: boolean;
  isInterrupt?: boolean;
  interruptType?: "human_feedback" | "step_feedback";
  stepFeedbackData?: {
    step_title: string;
    step_description: string;
    step_type: "research" | "processing";
    execution_result: string;
    has_errors?: boolean;
    error_reasons?: string[];
    error_summary?: string;
  };
}

// Store State
interface AgentState {
  responding: boolean;
  threadId: string | undefined;
  messageIds: string[];
  messages: Map<string, Message>;
  researchIds: string[];
  researchPlanIds: Map<string, string>;
  researchReportIds: Map<string, string>;
  researchActivityIds: Map<string, string[]>;
  ongoingResearchId: string | null;
  openResearchId: string | null;
  stepFeedbackMessageId: string | null;
  stepFeedbackData: {
    step_title: string;
    step_description: string;
    step_type: "research" | "processing";
    execution_result: string;
    has_errors?: boolean;
    error_reasons?: string[];
    error_summary?: string;
  } | null;
}

// Store Actions
interface AgentActions {
  appendMessage: (message: Message) => void;
  updateMessage: (message: Message) => void;
  updateMessages: (messages: Message[]) => void;
  openResearch: (researchId: string | null) => void;
  closeResearch: () => void;
  setOngoingResearch: (researchId: string | null) => void;
  setStepFeedback: (messageId: string, data: {
    step_title: string;
    step_description: string;
    step_type: "research" | "processing";
    execution_result: string;
    has_errors?: boolean;
    error_reasons?: string[];
    error_summary?: string;
  }) => void;
  clearStepFeedback: () => void;
  setResponding: (responding: boolean) => void;
}

const THREAD_ID = nanoid();

// Create Zustand Store
export const useAgentStore = create<AgentState & AgentActions>()((set, get) => ({
  responding: false,
  threadId: THREAD_ID,
  messageIds: [],
  messages: new Map<string, Message>(),
  researchIds: [],
  researchPlanIds: new Map<string, string>(),
  researchReportIds: new Map<string, string>(),
  researchActivityIds: new Map<string, string[]>(),
  ongoingResearchId: null,
  openResearchId: null,
  stepFeedbackMessageId: null,
  stepFeedbackData: null,

  appendMessage(message: Message) {
    set((state) => ({
      messageIds: [...state.messageIds, message.id],
      messages: new Map(state.messages).set(message.id, message),
    }));
  },

  updateMessage(message: Message) {
    set((state) => ({
      messages: new Map(state.messages).set(message.id, message),
    }));
  },

  updateMessages(messages: Message[]) {
    set((state) => {
      const newMessages = new Map(state.messages);
      messages.forEach((m) => newMessages.set(m.id, m));
      return { messages: newMessages };
    });
  },

  openResearch(researchId: string | null) {
    set({ openResearchId: researchId });
  },

  closeResearch() {
    set({ openResearchId: null });
  },

  setOngoingResearch(researchId: string | null) {
    set({ ongoingResearchId: researchId });
  },

  setStepFeedback(messageId, data) {
    set({ stepFeedbackMessageId: messageId, stepFeedbackData: data });
  },

  clearStepFeedback() {
    set({ stepFeedbackMessageId: null, stepFeedbackData: null });
  },

  setResponding(responding: boolean) {
    set({ responding });
  },
}));

// Utility functions
function existsMessage(id: string) {
  return useAgentStore.getState().messageIds.includes(id);
}

function getMessage(id: string) {
  return useAgentStore.getState().messages.get(id);
}

function findMessageByToolCallId(toolCallId: string) {
  return Array.from(useAgentStore.getState().messages.values())
    .reverse()
    .find((message) => {
      if (message.toolCalls) {
        return message.toolCalls.some((toolCall) => toolCall.id === toolCallId);
      }
      return false;
    });
}

function appendMessage(message: Message) {
  if (
    message.agent === "coder" ||
    message.agent === "reporter" ||
    message.agent === "researcher"
  ) {
    if (!getOngoingResearchId()) {
      const id = message.id;
      appendResearch(id);
      openResearch(id);
    }
    appendResearchActivity(message);
  }
  useAgentStore.getState().appendMessage(message);
}

function updateMessage(message: Message) {
  if (
    getOngoingResearchId() &&
    message.agent === "reporter" &&
    !message.isStreaming
  ) {
    useAgentStore.getState().setOngoingResearch(null);
  }
  useAgentStore.getState().updateMessage(message);
}

function getOngoingResearchId() {
  return useAgentStore.getState().ongoingResearchId;
}

function appendResearch(researchId: string) {
  let planMessage: Message | undefined;
  const reversedMessageIds = [...useAgentStore.getState().messageIds].reverse();
  for (const messageId of reversedMessageIds) {
    const message = getMessage(messageId);
    if (message?.agent === "planner") {
      planMessage = message;
      break;
    }
  }
  if (!planMessage) return;
  
  const messageIds = [researchId];
  messageIds.unshift(planMessage.id);
  useAgentStore.setState({
    ongoingResearchId: researchId,
    researchIds: [...useAgentStore.getState().researchIds, researchId],
    researchPlanIds: new Map(useAgentStore.getState().researchPlanIds).set(
      researchId,
      planMessage.id,
    ),
    researchActivityIds: new Map(useAgentStore.getState().researchActivityIds).set(
      researchId,
      messageIds,
    ),
  });
}

function appendResearchActivity(message: Message) {
  const researchId = getOngoingResearchId();
  if (researchId) {
    const researchActivityIds = useAgentStore.getState().researchActivityIds;
    const current = researchActivityIds.get(researchId)!;
    if (!current.includes(message.id)) {
      useAgentStore.setState({
        researchActivityIds: new Map(researchActivityIds).set(researchId, [
          ...current,
          message.id,
        ]),
      });
    }
    if (message.agent === "reporter") {
      useAgentStore.setState({
        researchReportIds: new Map(useAgentStore.getState().researchReportIds).set(
          researchId,
          message.id,
        ),
      });
    }
  }
}

// Message merge function (simplified version)
function mergeMessage(existingMessage: Message, event: any): Message {
  const { type, data } = event;
  
  switch (type) {
    case "content":
      return {
        ...existingMessage,
        content: existingMessage.content + data.content,
        contentChunks: [...existingMessage.contentChunks, data.content],
      };
    case "reasoning":
      return {
        ...existingMessage,
        reasoningContent: (existingMessage.reasoningContent || "") + data.content,
        reasoningContentChunks: [...(existingMessage.reasoningContentChunks || []), data.content],
      };
    case "tool_call":
      const newToolCall: ToolCallRuntime = {
        id: data.id,
        name: data.name,
        args: data.args,
        argsChunks: [JSON.stringify(data.args)],
      };
      return {
        ...existingMessage,
        toolCalls: [...(existingMessage.toolCalls || []), newToolCall],
      };
    case "tool_call_result":
      const updatedToolCalls = existingMessage.toolCalls?.map(tc => 
        tc.id === data.tool_call_id ? { ...tc, result: data.result } : tc
      );
      return {
        ...existingMessage,
        toolCalls: updatedToolCalls,
      };
    case "interrupt":
      return {
        ...existingMessage,
        finishReason: "interrupt",
        options: data.options,
        isStreaming: false,
      };
    case "finish":
      return {
        ...existingMessage,
        finishReason: data.reason || "stop",
        isStreaming: false,
      };
    default:
      return existingMessage;
  }
}

// Main send message function with WebSocket support
export async function sendMessage(
  content?: string,
  {
    interruptFeedback,
    resources,
    enabledTools
  }: {
    interruptFeedback?: string;
    resources?: Array<Resource>;
    enabledTools?: Array<string>;
  } = {},
  options: { abortSignal?: AbortSignal } = {},
) {
  if (content != null) {
    appendMessage({
      id: nanoid(),
      threadId: THREAD_ID,
      role: "user",
      content: content,
      contentChunks: [content],
      resources,
    });
  }

  const wsClient = getWebSocketClient();
  const { setResponding } = useAgentStore.getState();
  
  setResponding(true);
  let messageId: string | undefined;
  
  try {
    // Try WebSocket first
    if (wsClient && wsClient.isConnected()) {
      await sendMessageViaWebSocket(content, {
        interruptFeedback,
        resources,
        enabledTools,
      }, options);
    } else {
      // Fallback to mock implementation
      await sendMessageMock(content, {
        interruptFeedback,
        resources,
        enabledTools,
      }, options);
    }
  } catch (error) {
    console.error("Error sending message:", error);
    toast("An error occurred while generating the response. Please try again.");
    
    if (messageId != null) {
      const message = getMessage(messageId);
      if (message?.isStreaming) {
        message.isStreaming = false;
        useAgentStore.getState().updateMessage(message);
      }
    }
    useAgentStore.getState().setOngoingResearch(null);
  } finally {
    setResponding(false);
  }
}

// WebSocket implementation
async function sendMessageViaWebSocket(
  content?: string,
  options: {
    interruptFeedback?: string;
    resources?: Array<Resource>;
    enabledTools?: Array<string>;
  } = {},
  { abortSignal }: { abortSignal?: AbortSignal } = {},
) {
  const wsClient = getWebSocketClient();
  if (!wsClient || !wsClient.isConnected()) {
    throw new Error("WebSocket not connected");
  }

  // Send agent message request
  wsClient.emit("agent_message", {
    content: content ?? "[REPLAY]",
    thread_id: THREAD_ID,
    interrupt_feedback: options.interruptFeedback,
    resources: options.resources,
    enabled_tools: options.enabledTools,
    // Default settings
    auto_accepted_plan: false,
    enable_deep_thinking: true,
    enable_background_investigation: true,
    max_plan_iterations: 3,
    max_step_num: 10,
    max_search_results: 5,
    report_style: "detailed",
  });

  // Listen for responses
  return new Promise<void>((resolve, reject) => {
    const cleanup = () => {
      wsClient.off("agent_stream", handleAgentStream);
      wsClient.off("agent_error", handleError);
      wsClient.off("agent_complete", handleComplete);
    };

    const handleAgentStream = (event: any) => {
      try {
        processAgentEvent(event);
      } catch (err) {
        cleanup();
        reject(err);
      }
    };

    const handleError = (error: any) => {
      cleanup();
      reject(new Error(error.message || "Agent processing failed"));
    };

    const handleComplete = () => {
      cleanup();
      resolve();
    };

    // Handle abort signal
    if (abortSignal) {
      const abortHandler = () => {
        cleanup();
        wsClient.emit("agent_cancel", { thread_id: THREAD_ID });
        reject(new Error("Aborted"));
      };
      abortSignal.addEventListener("abort", abortHandler);
    }

    wsClient.on("agent_stream", handleAgentStream);
    wsClient.on("agent_error", handleError);
    wsClient.on("agent_complete", handleComplete);
  });
}

// Process agent events from WebSocket
function processAgentEvent(event: any) {
  const { type, data } = event;
  let messageId = data.id;
  let message: Message | undefined;

  if (type === "step_feedback") {
    const stepFeedbackData = {
      step_title: data.step_title,
      step_description: data.step_description,
      step_type: data.step_type,
      execution_result: data.execution_result,
      has_errors: data.has_errors,
      error_reasons: data.error_reasons,
      error_summary: data.error_summary,
    };
    useAgentStore.getState().setStepFeedback(messageId, stepFeedbackData);
    message = {
      id: messageId,
      threadId: data.thread_id,
      agent: data.agent,
      role: data.role,
      content: data.execution_result,
      contentChunks: [data.execution_result],
      isInterrupt: true,
      interruptType: "step_feedback",
      stepFeedbackData: stepFeedbackData,
    };
    appendMessage(message);
    return;
  }

  if (type === "tool_call_result") {
    message = findMessageByToolCallId(data.tool_call_id);
  } else if (!existsMessage(messageId)) {
    message = {
      id: messageId,
      threadId: data.thread_id,
      agent: data.agent,
      role: data.role,
      content: "",
      contentChunks: [],
      reasoningContent: "",
      reasoningContentChunks: [],
      isStreaming: true,
    };
    appendMessage(message);
  }

  message ??= getMessage(messageId);
  if (message) {
    message = mergeMessage(message, event);
    updateMessage(message);
  }
}

// Mock implementation for testing
async function sendMessageMock(
  content?: string,
  options: any = {},
  { abortSignal }: { abortSignal?: AbortSignal } = {},
) {
  // This is a simplified mock implementation
  // In a real scenario, this would simulate the agent workflow
  const messageId = nanoid();
  const message: Message = {
    id: messageId,
    threadId: THREAD_ID,
    role: "assistant",
    agent: "planner",
    content: "",
    contentChunks: [],
    isStreaming: true,
  };
  
  appendMessage(message);
  
  // Simulate streaming response
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const finalMessage = {
    ...message,
    content: `I understand you want to research: "${content}". Let me break this down into a research plan...`,
    contentChunks: [`I understand you want to research: "${content}". Let me break this down into a research plan...`],
    isStreaming: false,
    finishReason: "stop" as const,
  };
  
  updateMessage(finalMessage);
}

// Export research functions
export function openResearch(researchId: string | null) {
  useAgentStore.getState().openResearch(researchId);
}

export function closeResearch() {
  useAgentStore.getState().closeResearch();
}

// Export hooks
export function useResearchMessage(researchId: string) {
  return useAgentStore(
    useShallow((state) => {
      const messageId = state.researchPlanIds.get(researchId);
      return messageId ? state.messages.get(messageId) : undefined;
    }),
  );
}

export function useMessage(messageId: string | null | undefined) {
  return useAgentStore(
    useShallow((state) =>
      messageId ? state.messages.get(messageId) : undefined,
    ),
  );
}

export function useMessageIds() {
  return useAgentStore(useShallow((state) => state.messageIds));
}

export function useResponding() {
  return useAgentStore(useShallow((state) => state.responding));
}

export function useResearchIds() {
  return useAgentStore(useShallow((state) => state.researchIds));
}

export function useOpenResearchId() {
  return useAgentStore(useShallow((state) => state.openResearchId));
}

export function useStepFeedback() {
  return useAgentStore(useShallow((state) => ({
    messageId: state.stepFeedbackMessageId,
    data: state.stepFeedbackData,
  })));
}